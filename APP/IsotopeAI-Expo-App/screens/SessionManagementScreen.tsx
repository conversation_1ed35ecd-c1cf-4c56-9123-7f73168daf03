import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';
import { useSessionManagement } from '@/hooks/useSessionManagement';
import { Button, Card, Input } from '@/components/ui';
import { 
  SessionCompletionFlow, 
  SessionFeedbackCard, 
  SessionStatsCard 
} from '@/components/session';
import { TimerSession, SessionSummary } from '@/types/app';

export function SessionManagementScreen() {
  const theme = useTheme();
  const {
    sessions,
    currentSession,
    isLoading,
    error,
    createAndStartSession,
    completeCurrentSession,
    endCurrentSession,
    updateCurrentSession,
    refreshSessions,
    getTodaySessions,
    getThisWeekSessions,
    getSessionStats,
    hasActiveSession,
    getCurrentSessionDuration,
    dismissError,
  } = useSessionManagement();

  const [showCompletionFlow, setShowCompletionFlow] = useState(false);
  const [showFeedbackCard, setShowFeedbackCard] = useState(false);
  const [completionType, setCompletionType] = useState<'complete' | 'pause' | 'cancel'>('complete');
  const [currentDuration, setCurrentDuration] = useState(0);

  // Update current session duration every second
  useEffect(() => {
    if (!hasActiveSession()) return;

    const interval = setInterval(() => {
      setCurrentDuration(getCurrentSessionDuration());
    }, 1000);

    return () => clearInterval(interval);
  }, [hasActiveSession, getCurrentSessionDuration]);

  // Dismiss error when component mounts
  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: dismissError }
      ]);
    }
  }, [error, dismissError]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartSession = async () => {
    const session = await createAndStartSession({
      subject: 'Test Subject',
      mode: 'stopwatch',
      taskName: 'Demo Session',
      taskType: 'General Study',
    });

    if (session) {
      Alert.alert('Success', 'Session started successfully!');
    }
  };

  const handleCompleteSession = () => {
    setCompletionType('complete');
    setShowCompletionFlow(true);
  };

  const handlePauseSession = () => {
    setCompletionType('pause');
    setShowCompletionFlow(true);
  };

  const handleCancelSession = () => {
    setCompletionType('cancel');
    setShowCompletionFlow(true);
  };

  const handleSessionSaved = (session: TimerSession) => {
    setShowCompletionFlow(false);
    Alert.alert('Success', 'Session saved successfully!');
  };

  const handleFeedbackSubmit = async (summary: SessionSummary) => {
    if (currentSession) {
      const session = await completeCurrentSession(summary);
      if (session) {
        setShowFeedbackCard(false);
        Alert.alert('Success', 'Session feedback saved!');
      }
    }
  };

  const handleFeedbackSkip = () => {
    setShowFeedbackCard(false);
  };

  const todaySessions = getTodaySessions();
  const weekSessions = getThisWeekSessions();
  const stats = getSessionStats();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          Session Management
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
          Manage your study sessions and feedback
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refreshSessions}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Current Session Card */}
        {hasActiveSession() && currentSession && (
          <Card
            variant="elevated"
            style={[
              styles.currentSessionCard,
              { 
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.primary,
                borderWidth: 2,
              }
            ]}
          >
            <View style={styles.currentSessionHeader}>
              <MaterialIcons 
                name="play-circle" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.currentSessionIcon}
              />
              <View style={styles.currentSessionInfo}>
                <Text style={[styles.currentSessionTitle, { color: theme.colors.text.primary }]}>
                  Active Session
                </Text>
                <Text style={[styles.currentSessionDuration, { color: theme.colors.primary }]}>
                  {formatDuration(currentDuration)}
                </Text>
              </View>
            </View>

            {currentSession.subject && (
              <Text style={[styles.currentSessionSubject, { color: theme.colors.text.secondary }]}>
                Subject: {currentSession.subject}
              </Text>
            )}

            <View style={styles.currentSessionActions}>
              <Button
                title="Complete"
                onPress={handleCompleteSession}
                variant="primary"
                size="sm"
                style={styles.sessionActionButton}
                icon={<MaterialIcons name="check" size={16} color={theme.colors.background} />}
              />
              <Button
                title="Pause"
                onPress={handlePauseSession}
                variant="outline"
                size="sm"
                style={styles.sessionActionButton}
                icon={<MaterialIcons name="pause" size={16} color={theme.colors.text.primary} />}
              />
              <Button
                title="Cancel"
                onPress={handleCancelSession}
                variant="outline"
                size="sm"
                style={[styles.sessionActionButton, { borderColor: '#EF4444' }]}
                titleStyle={{ color: '#EF4444' }}
                icon={<MaterialIcons name="close" size={16} color="#EF4444" />}
              />
            </View>
          </Card>
        )}

        {/* Quick Actions */}
        <Card
          variant="elevated"
          style={[
            styles.quickActionsCard,
            { 
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
            }
          ]}
        >
          <Text style={[styles.cardTitle, { color: theme.colors.text.primary }]}>
            Quick Actions
          </Text>
          
          <View style={styles.quickActions}>
            <Button
              title="Start Session"
              onPress={handleStartSession}
              variant="primary"
              size="lg"
              style={styles.quickActionButton}
              disabled={hasActiveSession() || isLoading}
              icon={<MaterialIcons name="play-arrow" size={20} color={theme.colors.background} />}
            />
            
            <Button
              title="Show Feedback"
              onPress={() => setShowFeedbackCard(true)}
              variant="outline"
              size="lg"
              style={styles.quickActionButton}
              icon={<MaterialIcons name="feedback" size={20} color={theme.colors.text.primary} />}
            />
          </View>
        </Card>

        {/* Session Statistics */}
        <SessionStatsCard
          sessions={sessions}
          title="Overall Statistics"
          showDetailedStats={true}
          style={styles.statsCard}
        />

        {/* Today's Sessions */}
        {todaySessions.length > 0 && (
          <SessionStatsCard
            sessions={todaySessions}
            title="Today's Sessions"
            showDetailedStats={false}
            style={styles.statsCard}
          />
        )}

        {/* This Week's Sessions */}
        {weekSessions.length > 0 && (
          <SessionStatsCard
            sessions={weekSessions}
            title="This Week's Sessions"
            showDetailedStats={false}
            style={styles.statsCard}
          />
        )}

        {/* Demo Feedback Card */}
        {showFeedbackCard && (
          <SessionFeedbackCard
            onSubmit={handleFeedbackSubmit}
            onSkip={handleFeedbackSkip}
            isLoading={isLoading}
            title="Demo Feedback"
            subtitle="This is a demo of the session feedback component"
          />
        )}
      </ScrollView>

      {/* Session Completion Flow */}
      {showCompletionFlow && currentSession && (
        <SessionCompletionFlow
          visible={showCompletionFlow}
          onClose={() => setShowCompletionFlow(false)}
          session={currentSession}
          userId={currentSession.id} // This should be user ID in real usage
          completionType={completionType}
          onSessionSaved={handleSessionSaved}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  currentSessionCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  currentSessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentSessionIcon: {
    marginRight: 12,
  },
  currentSessionInfo: {
    flex: 1,
  },
  currentSessionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  currentSessionDuration: {
    fontSize: 20,
    fontWeight: '700',
    fontFamily: 'monospace',
  },
  currentSessionSubject: {
    fontSize: 14,
    marginBottom: 16,
  },
  currentSessionActions: {
    flexDirection: 'row',
    gap: 8,
  },
  sessionActionButton: {
    flex: 1,
    borderRadius: 8,
  },
  quickActionsCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  quickActions: {
    gap: 12,
  },
  quickActionButton: {
    borderRadius: 12,
  },
  statsCard: {
    marginBottom: 20,
  },
});
