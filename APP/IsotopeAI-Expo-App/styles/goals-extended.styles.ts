import { StyleSheet, Dimensions } from 'react-native';
import { Theme } from '@/types/theme';

const { width: screenWidth } = Dimensions.get('window');
const scale = (size: number) => (screenWidth / 375) * size; // Base screen width for scaling
const horizontalPadding = scale(24);
const verticalPadding = scale(20);

export const createGoalsExtendedStyles = (theme: Theme) => StyleSheet.create({
  // Progress Slider Styles
  progressSliderContainer: {
    marginTop: scale(12),
    paddingTop: scale(12),
    borderTopWidth: scale(1),
    borderTopColor: '#F3F4F6',
  },
  sliderLabel: {
    fontSize: scale(11),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: scale(8),
  },
  progressSlider: {
    paddingVertical: scale(8),
  },
  progressSliderTrack: {
    height: scale(20),
    backgroundColor: '#F3F4F6',
    borderRadius: scale(10),
    position: 'relative',
    justifyContent: 'center',
  },
  progressSliderFill: {
    height: scale(6),
    backgroundColor: '#3B82F6',
    borderRadius: scale(3),
    position: 'absolute',
    top: scale(7),
    left: 0,
  },
  progressSliderThumb: {
    width: scale(16),
    height: scale(16),
    backgroundColor: '#3B82F6',
    borderRadius: scale(8),
    position: 'absolute',
    top: scale(2),
    marginLeft: scale(-8),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2) },
    shadowOpacity: 0.2,
    shadowRadius: scale(2),
    elevation: scale(3),
  },
  taskMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskMetaLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12),
  },
  taskMetaRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12),
  },
  categoryTag: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: scale(12),
    color: '#6B7280',
    fontWeight: '500',
  },
  priorityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(4),
  },
  priorityText: {
    fontSize: scale(12),
    fontWeight: '500',
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(4),
  },
  dueDateText: {
    fontSize: scale(12),
    color: '#6B7280',
  },
  overdueDateText: {
    color: '#EF4444',
    fontWeight: '500',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(4),
  },
  durationText: {
    fontSize: scale(12),
    color: '#6B7280',
  },
  statsSection: {
    paddingHorizontal: horizontalPadding,
    marginVertical: scale(16),
  },
  statsGrid: {
    flexDirection: 'row',
    gap: scale(12),
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: scale(16),
    padding: scale(16),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4),
    elevation: scale(3),
  },
  statValue: {
    fontSize: scale(20),
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginVertical: scale(4),
  },
  statLabel: {
    fontSize: scale(12),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: horizontalPadding,
    marginVertical: scale(16),
  },
  sectionTitle: {
    fontSize: scale(18),
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: scale(16),
  },
  goalMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(4),
  },
  goalDateText: {
    fontSize: scale(12),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  goalPriority: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(4),
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: scale(40),
  },
  emptyTitle: {
    fontSize: scale(18),
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: scale(16),
    marginBottom: scale(8),
  },
  emptyText: {
    fontSize: scale(14),
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: horizontalPadding,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(20),
    width: '100%',
    maxWidth: scale(400),
    maxHeight: '90%',
  },
  filterModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(20),
    width: '100%',
    maxWidth: scale(400),
    maxHeight: '80%',
  },
  categorySelector: {
    paddingVertical: scale(8),
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    marginRight: scale(12),
    borderRadius: scale(12),
    backgroundColor: '#F9FAFB',
    borderWidth: scale(1),
    borderColor: '#E5E7EB',
  },
  categoryOptionActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  categoryOptionText: {
    fontSize: scale(14),
    color: '#6B7280',
    fontWeight: '500',
  },
  categoryOptionActiveText: {
    color: '#6366F1',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: scale(12),
  },
  checkbox: {
    width: scale(20),
    height: scale(20),
    borderRadius: scale(4),
    borderWidth: scale(2),
    borderColor: '#D1D5DB',
    marginRight: scale(12),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: scale(16),
    color: '#1F2937',
  },
  filterSection: {
    marginBottom: scale(24),
  },
  filterSectionTitle: {
    fontSize: scale(16),
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: scale(12),
  },
  filterOptions: {
    gap: scale(8),
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    borderRadius: scale(12),
    backgroundColor: '#F9FAFB',
    borderWidth: scale(1),
    borderColor: '#E5E7EB',
  },
  filterOptionActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  filterOptionText: {
    fontSize: scale(14),
    color: '#6B7280',
    fontWeight: '500',
  },
  filterOptionActiveText: {
    color: '#6366F1',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: verticalPadding,
    borderBottomWidth: scale(1),
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: scale(20),
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  modalBody: {
    padding: verticalPadding,
    maxHeight: scale(400),
  },
  inputGroup: {
    marginBottom: scale(20),
  },
  inputLabel: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: scale(8),
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: scale(1),
    borderColor: '#D1D5DB',
    borderRadius: scale(12),
    padding: scale(16),
    fontSize: scale(16),
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  textArea: {
    height: scale(80),
    textAlignVertical: 'top',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: scale(8),
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    backgroundColor: '#F9FAFB',
    borderRadius: scale(12),
    borderWidth: scale(2),
    borderColor: '#E5E7EB',
    gap: scale(6),
  },
  priorityButtonActive: {
    backgroundColor: '#F3F4F6',
  },
  priorityButtonText: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalActions: {
    padding: verticalPadding,
    gap: scale(16),
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: scale(8),
    paddingVertical: scale(12),
    backgroundColor: '#FEF2F2',
    borderRadius: scale(12),
    borderWidth: scale(1),
    borderColor: '#FECACA',
  },
  deleteText: {
    fontSize: scale(16),
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: scale(12),
  },
  cancelButton: {
    flex: 1,
    paddingVertical: scale(14),
    backgroundColor: '#F3F4F6',
    borderRadius: scale(12),
    alignItems: 'center',
  },
  cancelText: {
    fontSize: scale(16),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  saveButton: {
    flex: 1,
    borderRadius: scale(12),
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: scale(14),
    alignItems: 'center',
  },
  saveText: {
    fontSize: scale(16),
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  // Progress Chart Styles
  progressChart: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(16),
    padding: verticalPadding,
    marginHorizontal: scale(16),
    marginVertical: scale(8),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4),
    elevation: scale(3),
  },
  chartTitle: {
    fontSize: scale(18),
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: scale(16),
  },
  chartContainer: {
    gap: scale(12),
  },
  chartBar: {
    gap: scale(8),
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12),
  },
  barBackground: {
    height: scale(8),
    backgroundColor: '#F3F4F6',
    borderRadius: scale(4),
    overflow: 'hidden',
    flex: 1,
  },
  barFill: {
    height: '100%',
    borderRadius: scale(4),
  },
  progressPercentage: {
    fontSize: scale(12),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    minWidth: scale(35),
    textAlign: 'right',
  },
  barInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  taskCount: {
    fontSize: scale(12),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  // Milestone Progress Styles
  milestoneProgress: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(16),
    padding: verticalPadding,
    marginHorizontal: scale(16),
    marginVertical: scale(8),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4),
    elevation: scale(3),
  },
  milestoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
    marginBottom: scale(16),
  },
  milestoneTitle: {
    fontSize: scale(18),
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  milestoneBar: {
    height: scale(12),
    backgroundColor: '#FEF3C7',
    borderRadius: scale(6),
    overflow: 'hidden',
    marginBottom: scale(12),
  },
  milestoneBarFill: {
    height: '100%',
    backgroundColor: '#F59E0B',
    borderRadius: scale(6),
  },
  milestoneStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milestoneStatsText: {
    fontSize: scale(14),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  milestonePercentage: {
    fontSize: scale(16),
    fontFamily: 'Inter-SemiBold',
    color: '#F59E0B',
  },
  // Productivity Analytics Styles
  productivityAnalytics: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(16),
    padding: verticalPadding,
    marginHorizontal: scale(16),
    marginVertical: scale(8),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4),
    elevation: scale(3),
  },
  analyticsTitle: {
    fontSize: scale(18),
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: scale(16),
  },
  analyticsOverview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: scale(20),
  },
  analyticsCard: {
    flex: 1,
    alignItems: 'center',
    padding: scale(12),
    backgroundColor: '#F9FAFB',
    borderRadius: scale(12),
    marginHorizontal: scale(4),
  },
  analyticsValue: {
    fontSize: scale(20),
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginTop: scale(8),
    marginBottom: scale(4),
  },
  analyticsLabel: {
    fontSize: scale(12),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  priorityBreakdown: {
    gap: scale(12),
  },
  breakdownTitle: {
    fontSize: scale(16),
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: scale(12),
  },
  priorityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: scale(8),
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  priorityDot: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(4),
  },
  priorityName: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#374151',
  },
  priorityStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12),
  },
  priorityCount: {
    fontSize: scale(14),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  priorityRate: {
    fontSize: scale(14),
    fontFamily: 'Inter-SemiBold',
    minWidth: scale(40),
    textAlign: 'right',
  },
  // Reminder Settings Styles
  reminderTimeContainer: {
    marginTop: scale(12),
    paddingTop: scale(12),
    borderTopWidth: scale(1),
    borderTopColor: '#F3F4F6',
  },
  reminderTimeLabel: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: scale(8),
  },
  reminderHint: {
    fontSize: scale(12),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: scale(4),
    fontStyle: 'italic',
  },
  // Date/Time Picker Styles
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: scale(1),
    borderColor: '#E5E7EB',
    borderRadius: scale(12),
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    gap: scale(12),
  },
  datePickerText: {
    fontSize: scale(16),
    color: '#1F2937',
    flex: 1,
  },
  dateTimePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12),
  },
  dateTimePickerText: {
    fontSize: scale(16),
    color: '#1F2937',
    flex: 1,
  },
  reminderIntervalContainer: {
    marginTop: scale(12),
  },
  reminderIntervalLabel: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: scale(8),
  },
  intervalSelector: {
    flexDirection: 'row',
    gap: scale(8),
  },
  intervalButton: {
    flex: 1,
    paddingVertical: scale(8),
    paddingHorizontal: scale(12),
    borderRadius: scale(8),
    borderWidth: scale(1),
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  intervalButtonActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  intervalButtonText: {
    fontSize: scale(14),
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  intervalButtonActiveText: {
    color: theme.colors.text.inverse,
  },
});

// Legacy export for backward compatibility
export const goalsExtendedStyles = createGoalsExtendedStyles;
