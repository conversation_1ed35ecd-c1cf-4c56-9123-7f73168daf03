import { StyleSheet, Dimensions } from 'react-native';
import { Theme } from '@/types/theme';

const { width } = Dimensions.get('window');

export const createTimerStyles = (theme: Theme) => {
  const { colors, spacing, borderRadius } = theme;

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background.primary,
    },
    scrollContent: {
      paddingBottom: spacing.xxl,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.lg,
      paddingTop: 60, // Adjust as needed for status bar
      paddingBottom: spacing.md,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.md,
    },
    settingsButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.card,
    },
    subjectSection: {
      paddingHorizontal: spacing.lg,
      marginVertical: spacing.lg,
    },
    statsSection: {
      paddingHorizontal: spacing.lg,
      marginBottom: spacing.lg,
    },
    statsGrid: {
      flexDirection: 'row',
      gap: spacing.md,
    },
    statCard: {
      flex: 1,
      backgroundColor: colors.background.card,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      alignItems: 'center',
      gap: spacing.xs,
    },
    statValue: {
      fontSize: 26,
      fontWeight: '600',
      color: colors.text.primary,
    },
    statLabel: {
      fontSize: 13,
      fontWeight: '500',
      color: colors.text.secondary,
      textTransform: 'uppercase',
    },
    timerSection: {
      alignItems: 'center',
      marginVertical: spacing.xl,
      paddingHorizontal: spacing.lg,
    },
    timerCard: {
      width: '100%',
      borderRadius: borderRadius.xl,
      padding: spacing.xl,
      marginBottom: spacing.xl,
      backgroundColor: colors.background.card,
    },
    timerHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.lg,
      gap: spacing.md,
    },
    phaseIndicator: {
      width: 48,
      height: 48,
      borderRadius: borderRadius.full,
      justifyContent: 'center',
      alignItems: 'center',
    },
    phaseIcon: {
      fontSize: 24,
    },
    timerInfo: {
      flex: 1,
    },
    timerLabel: {
      fontSize: 24,
      fontWeight: '600',
      color: colors.text.primary,
    },
    cycleText: {
      fontSize: 15,
      fontWeight: '500',
      color: colors.text.secondary,
    },
    timerDisplay: {
      alignItems: 'center',
      marginVertical: spacing.lg,
    },
    timerText: {
      fontSize: width * 0.18,
      fontWeight: '800',
      color: colors.text.primary,
      fontFamily: 'SF Pro Display',
      textAlign: 'center',
      letterSpacing: -2,
    },
    progressContainer: {
      width: '100%',
      alignItems: 'center',
      marginTop: spacing.md,
    },
    progressTrack: {
      width: '100%',
      height: 6,
      borderRadius: 3,
      backgroundColor: colors.background.secondary,
    },
    progressFill: {
      height: '100%',
      borderRadius: 3,
    },
    timerControls: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      width: '100%',
      marginTop: spacing.md,
    },
    controlButton: {
      width: 72,
      height: 72,
      borderRadius: 36,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background.card,
    },
    playButton: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: colors.accent.primary,
      transform: [{ scale: 1.1 }],
    },
    section: {
      paddingHorizontal: spacing.lg,
      marginVertical: spacing.lg,
    },
    blockingSectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    sectionTitle: {
      fontSize: 28,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing.md,
    },
    soundGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: spacing.md,
    },
    soundCard: {
      width: (width - (spacing.lg * 2) - spacing.md) / 2,
      backgroundColor: colors.background.card,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      alignItems: 'center',
      gap: spacing.sm,
    },
    soundIcon: {
      fontSize: 32,
    },
    soundName: {
      fontSize: 16,
      fontWeight: '400',
      color: colors.text.primary,
    },
    activeBadge: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    blockingCard: {
      backgroundColor: colors.background.card,
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
    },
    blockingHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: spacing.md,
      marginBottom: spacing.md,
    },
    blockingTitle: {
      fontSize: 22,
      fontWeight: '600',
      color: colors.text.primary,
      flex: 1,
    },
    statusBadge: {
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
      borderRadius: borderRadius.sm,
    },
    statusText: {
      fontSize: 13,
      fontWeight: '600',
      color: colors.text.primary,
    },
    blockingDescription: {
      fontSize: 17,
      fontWeight: '400',
      color: colors.text.secondary,
      lineHeight: 22,
      marginBottom: spacing.md,
    },
    manualBlockingButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: borderRadius.md,
      paddingVertical: spacing.md,
      backgroundColor: colors.background.secondary,
    },
    manualBlockingButtonText: {
      fontSize: 16,
      fontWeight: '600',
      marginLeft: spacing.sm,
    },
    progressCard: {
      backgroundColor: colors.background.card,
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    statItem: {
      alignItems: 'center',
      gap: spacing.xs,
    },
    tipCard: {
      backgroundColor: colors.background.card,
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
    },
    tipTitle: {
      fontSize: 22,
      fontWeight: '600',
      color: colors.text.primary,
      marginBottom: spacing.sm,
    },
    tipText: {
      fontSize: 17,
      fontWeight: '400',
      color: colors.text.secondary,
      lineHeight: 22,
    },
  });
};
