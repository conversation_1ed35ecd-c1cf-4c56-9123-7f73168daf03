import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Subject, ApiResponse, RealtimeSubscription } from '@/types/app';
import { enhancedSupabaseService } from '@/services/enhancedSupabaseService';
import { supabaseService } from '@/services/supabaseService';

interface SubjectState {
  subjects: Subject[];
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
  subscription: RealtimeSubscription | null;
  
  // Actions
  setSubjects: (subjects: Subject[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  fetchSubjects: (userId: string, forceRefresh?: boolean) => Promise<Subject[]>;
  addSubject: (userId: string, name: string, color: string) => Promise<Subject>;
  updateSubject: (subjectId: string, updates: Partial<Subject>) => Promise<Subject>;
  deleteSubject: (subjectId: string) => Promise<void>;
  getSubjectById: (id: string) => Subject | undefined;
  getSubjectByName: (name: string) => Subject | undefined;
  validateSubjectName: (name: string, excludeId?: string) => boolean;
  subscribeToRealtime: (userId: string) => void;
  unsubscribeFromRealtime: () => void;
  reset: () => void;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const initialState = {
  subjects: [],
  isLoading: false,
  error: null,
  lastFetched: null,
  subscription: null,
};

export const useSubjectStore = create<SubjectState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setSubjects: (subjects) => {
        set({ 
          subjects, 
          isLoading: false, 
          lastFetched: Date.now(),
          error: null 
        });
      },
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),
      
      fetchSubjects: async (userId: string, forceRefresh = false) => {
        const state = get();
        
        // Check cache validity
        if (!forceRefresh && state.lastFetched && 
            Date.now() - state.lastFetched < CACHE_DURATION && 
            state.subjects.length > 0) {
          return state.subjects;
        }

        set({ isLoading: true, error: null });
        
        try {
          const response = await enhancedSupabaseService.getSubjects(userId);
          
          if (response.success && response.data) {
            const subjects = response.data;
            get().setSubjects(subjects);
            
            // Set up real-time subscription if not already active
            if (!state.subscription) {
              get().subscribeToRealtime(userId);
            }
            
            return subjects;
          } else {
            throw new Error(response.error?.message || 'Failed to fetch subjects');
          }
        } catch (error: any) {
          console.error('Error fetching subjects:', error);
          set({ error: error.message, isLoading: false });
          
          // Return cached subjects if available
          return state.subjects;
        }
      },
      
      addSubject: async (userId: string, name: string, color: string) => {
        const state = get();
        
        // Validate subject name
        if (!get().validateSubjectName(name)) {
          throw new Error('Subject name already exists');
        }
        
        try {
          const response = await enhancedSupabaseService.createSubject(userId, name, color);
          
          if (response.success && response.data) {
            const newSubject = response.data;
            
            // Update local state optimistically
            const currentSubjects = state.subjects;
            set({ 
              subjects: [...currentSubjects, newSubject],
              lastFetched: Date.now(),
              error: null
            });
            
            return newSubject;
          } else {
            throw new Error(response.error?.message || 'Failed to create subject');
          }
        } catch (error: any) {
          console.error('Error adding subject:', error);
          set({ error: error.message });
          throw error;
        }
      },
      
      updateSubject: async (subjectId: string, updates: Partial<Subject>) => {
        const state = get();
        
        // Validate subject name if being updated
        if (updates.name && !get().validateSubjectName(updates.name, subjectId)) {
          throw new Error('Subject name already exists');
        }
        
        try {
          const response = await enhancedSupabaseService.updateSubject(subjectId, updates);
          
          if (response.success && response.data) {
            const updatedSubject = response.data;
            
            // Update local state optimistically
            const currentSubjects = state.subjects;
            const updatedSubjects = currentSubjects.map(s => 
              s.id === subjectId ? updatedSubject : s
            );
            
            set({ 
              subjects: updatedSubjects,
              lastFetched: Date.now(),
              error: null
            });
            
            return updatedSubject;
          } else {
            throw new Error(response.error?.message || 'Failed to update subject');
          }
        } catch (error: any) {
          console.error('Error updating subject:', error);
          set({ error: error.message });
          throw error;
        }
      },
      
      deleteSubject: async (subjectId: string) => {
        const state = get();
        
        try {
          const response = await enhancedSupabaseService.deleteSubject(subjectId);
          
          if (response.success) {
            // Update local state optimistically
            const currentSubjects = state.subjects;
            const filteredSubjects = currentSubjects.filter(s => s.id !== subjectId);
            
            set({ 
              subjects: filteredSubjects,
              lastFetched: Date.now(),
              error: null
            });
          } else {
            throw new Error(response.error?.message || 'Failed to delete subject');
          }
        } catch (error: any) {
          console.error('Error deleting subject:', error);
          set({ error: error.message });
          throw error;
        }
      },
      
      getSubjectById: (id: string) => {
        return get().subjects.find(subject => subject.id === id);
      },
      
      getSubjectByName: (name: string) => {
        return get().subjects.find(subject => 
          subject.name.toLowerCase() === name.toLowerCase()
        );
      },
      
      validateSubjectName: (name: string, excludeId?: string): boolean => {
        const trimmedName = name.trim();
        if (!trimmedName) return false;
        
        const existingSubject = get().getSubjectByName(trimmedName);
        return !existingSubject || (excludeId ? existingSubject.id === excludeId : false);
      },
      
      subscribeToRealtime: (userId: string) => {
        const state = get();
        
        // Don't create multiple subscriptions
        if (state.subscription) {
          return;
        }
        
        try {
          const subscription = supabaseService.subscribeToUserSubjects(userId, (subjects) => {
            console.log('Real-time subjects update received:', subjects.length);
            get().setSubjects(subjects);
          });
          
          set({ subscription });
        } catch (error) {
          console.error('Error setting up real-time subscription:', error);
        }
      },
      
      unsubscribeFromRealtime: () => {
        const state = get();
        
        if (state.subscription) {
          try {
            state.subscription.unsubscribe();
            set({ subscription: null });
          } catch (error) {
            console.error('Error unsubscribing from real-time updates:', error);
          }
        }
      },
      
      reset: () => {
        get().unsubscribeFromRealtime();
        set(initialState);
      },
    }),
    {
      name: 'isotope-subject-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        subjects: state.subjects,
        lastFetched: state.lastFetched,
      }),
    }
  )
);
