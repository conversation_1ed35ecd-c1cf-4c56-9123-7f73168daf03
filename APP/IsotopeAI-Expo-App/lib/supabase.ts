import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabaseUrl = 'https://pcfrgvhigvklersufktf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjZnJndmhpZ3ZrbGVyc3Vma3RmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3NjA5MzcsImV4cCI6MjA2NDMzNjkzN30.sz7YpgMNQ8AT5PzTBy_MBtPNdE135R7hy2LU7YZO56g';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types based on the existing schema
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          username: string | null;
          created_at: string | null;
          updated_at: string | null;
          stats: any | null;
          progress: any | null;
          uid: string | null;
          backgroundImage: string | null;
          bio: string | null;
          location: string | null;
          display_name: string | null;
          photo_url: string | null;
          last_login: string | null;
          study_sessions_data: any | null;
          mock_tests_data: any | null;
          welcome_email_sent: boolean | null;
          member_since: string | null;
          profile_views: number | null;
          daily_target: number | null;
          daily_motivation: string | null;
          day_start_time: number | null;
        };
        Insert: {
          id?: string;
          email: string;
          username?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          stats?: any | null;
          progress?: any | null;
          uid?: string | null;
          backgroundImage?: string | null;
          bio?: string | null;
          location?: string | null;
          display_name?: string | null;
          photo_url?: string | null;
          last_login?: string | null;
          study_sessions_data?: any | null;
          mock_tests_data?: any | null;
          welcome_email_sent?: boolean | null;
          member_since?: string | null;
          profile_views?: number | null;
          daily_target?: number | null;
          daily_motivation?: string | null;
          day_start_time?: number | null;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          stats?: any | null;
          progress?: any | null;
          uid?: string | null;
          backgroundImage?: string | null;
          bio?: string | null;
          location?: string | null;
          display_name?: string | null;
          photo_url?: string | null;
          last_login?: string | null;
          study_sessions_data?: any | null;
          mock_tests_data?: any | null;
          welcome_email_sent?: boolean | null;
          member_since?: string | null;
          profile_views?: number | null;
          daily_target?: number | null;
          daily_motivation?: string | null;
          day_start_time?: number | null;
        };
      };
      study_sessions: {
        Row: {
          id: string;
          user_id: string;
          subject: string | null;
          duration: number;
          mode: string | null;
          phase: string | null;
          completed: boolean | null;
          start_time: string;
          end_time: string | null;
          notes: string | null;
          created_at: string | null;
          task_name: string | null;
          task_type: string | null;
          productivity_rating: number | null;
          feedback: string | null;
          date: string;
          subject_color: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          subject?: string | null;
          duration: number;
          mode?: string | null;
          phase?: string | null;
          completed?: boolean | null;
          start_time: string;
          end_time?: string | null;
          notes?: string | null;
          created_at?: string | null;
          task_name?: string | null;
          task_type?: string | null;
          productivity_rating?: number | null;
          feedback?: string | null;
          date: string;
          subject_color?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          subject?: string | null;
          duration?: number;
          mode?: string | null;
          phase?: string | null;
          completed?: boolean | null;
          start_time?: string;
          end_time?: string | null;
          notes?: string | null;
          created_at?: string | null;
          task_name?: string | null;
          task_type?: string | null;
          productivity_rating?: number | null;
          feedback?: string | null;
          date?: string;
          subject_color?: string | null;
        };
      };
      userSubjects: {
        Row: {
          id: string;
          userId: string;
          name: string;
          color: string | null;
          createdAt: string | null;
        };
        Insert: {
          id?: string;
          userId: string;
          name: string;
          color?: string | null;
          createdAt?: string | null;
        };
        Update: {
          id?: string;
          userId?: string;
          name?: string;
          color?: string | null;
          createdAt?: string | null;
        };
      };
      exams: {
        Row: {
          id: string;
          userId: string;
          name: string;
          date: string;
          totalMarks: number;
          totalMarksObtained: number;
          subjectMarks: any | null;
          notes: string | null;
          createdAt: string | null;
        };
        Insert: {
          id?: string;
          userId: string;
          name: string;
          date: string;
          totalMarks: number;
          totalMarksObtained: number;
          subjectMarks?: any | null;
          notes?: string | null;
          createdAt?: string | null;
        };
        Update: {
          id?: string;
          userId?: string;
          name?: string;
          date?: string;
          totalMarks?: number;
          totalMarksObtained?: number;
          subjectMarks?: any | null;
          notes?: string | null;
          createdAt?: string | null;
        };
      };
    };
  };
}
