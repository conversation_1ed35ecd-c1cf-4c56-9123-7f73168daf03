import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { Task } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

interface ProgressChartProps {
  tasks: Task[];
  screenWidth: number;
}

const ProgressChart: React.FC<ProgressChartProps> = ({ tasks, screenWidth }) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  const chartWidth = screenWidth - localScale(32);
  const chartHeight = localScale(200);

  // Calculate priority-based progress
  const priorities = ['urgent', 'high', 'medium', 'low'] as const;
  const { theme } = useTheme();
  const priorityColors = {
    urgent: theme.colors.status.error,
    high: theme.colors.status.warning,
    medium: theme.colors.status.info,
    low: theme.colors.status.success
  };

  const priorityProgress = priorities.map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const completedTasks = priorityTasks.filter(task => task.status === 'completed');
    const progress = priorityTasks.length > 0 ? (completedTasks.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      color: priorityColors[priority],
      progress,
      total: priorityTasks.length,
      completed: completedTasks.length,
    };
  });

  const maxProgress = Math.max(...priorityProgress.map(pp => pp.progress), 1);

  return (
    <View style={styles(screenWidth, theme).progressChart}>
      <Text style={styles(screenWidth, theme).chartTitle}>Progress by Priority</Text>
      <View style={styles(screenWidth, theme).chartContainer}>
        {priorityProgress.map((item) => (
          <View key={item.priority} style={styles(screenWidth, theme).chartBar}>
            <View style={styles(screenWidth, theme).barInfo}>
              <Text style={styles(screenWidth, theme).categoryName}>{item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}</Text>
              <Text style={styles(screenWidth, theme).taskCount}>{item.completed}/{item.total} tasks</Text>
            </View>
            <View style={styles(screenWidth, theme).barContainer}>
              <View style={[styles(screenWidth, theme).barBackground, { width: chartWidth * 0.7 }]}>
                <Animated.View
                  style={[
                    styles(screenWidth, theme).barFill,
                    {
                      backgroundColor: item.color,
                      width: `${item.progress}%`,
                    }
                  ]}
                />
              </View>
              <Text style={styles(screenWidth, theme).progressPercentage}>{Math.round(item.progress)}%</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = (screenWidth: number, theme: any) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    progressChart: {
      backgroundColor: theme.colors.background.secondary,
      borderRadius: localScale(16),
      padding: localScale(20),
      margin: localScale(16),
      shadowColor: '#000',
      shadowOffset: { width: 0, height: localScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: localScale(4),
      elevation: localScale(3),
    },
    chartTitle: {
      fontSize: localScale(18),
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: localScale(16),
    },
    chartContainer: {
      gap: localScale(12),
    },
    chartBar: {
      gap: localScale(8),
    },
    barContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(12),
    },
    barBackground: {
      height: localScale(8),
      backgroundColor: theme.colors.ui.progressBarTrack,
      borderRadius: localScale(4),
      overflow: 'hidden',
    },
  barFill: {
    height: '100%',
    borderRadius: localScale(4),
  },
    progressPercentage: {
      fontSize: localScale(12),
      fontWeight: '600',
      color: theme.colors.text.secondary,
      minWidth: localScale(35),
      textAlign: 'right',
    },
    barInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    categoryName: {
      fontSize: localScale(14),
      fontWeight: '500',
      color: theme.colors.text.primary,
    },
    taskCount: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
    },
  });
};

export default ProgressChart;
