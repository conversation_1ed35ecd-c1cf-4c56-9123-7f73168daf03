import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Animated from 'react-native-reanimated';
import {
  Star,
  CheckCircle,
  Award,
  Calendar as CalendarIcon,
  MoreVertical,
} from 'lucide-react-native';
import { Task } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

interface TaskCardProps {
  task: Task;
  onToggleComplete: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUpdateProgress: (progress: number) => void;
  getPriorityColor: (priority: Task['priority']) => string;
  getPriorityIcon: (priority: Task['priority']) => React.ReactNode;
  getStatusIcon: (status: Task['status']) => React.ReactNode;
  formatDate: (date: Date) => string;
  isOverdue: (date: Date) => boolean;
  getSubjectById: (id: string) => any;
  screenWidth: number;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onToggleComplete,
  onEdit,
  onUpdateProgress,
  getPriorityColor,
  getPriorityIcon,
  getStatusIcon,
  formatDate,
  isOverdue,
  getSubjectById,
  screenWidth,
}) => {
  const { theme } = useTheme();
  const localScale = (size: number) => (screenWidth / 375) * size;
  const subject = task.subject_id ? getSubjectById(task.subject_id) : null;
  const isTaskOverdue = task.due_date && isOverdue(task.due_date);

  const cardStyles = styles(screenWidth, theme);

  return (
    <View style={[
      cardStyles.taskCard,
      isTaskOverdue && cardStyles.overdueCard,
      task.status === 'completed' && cardStyles.completedCard
    ]}>
      {/* Task Header */}
      <View style={cardStyles.taskHeader}>
        <TouchableOpacity onPress={onToggleComplete} style={cardStyles.statusButton}>
          {getStatusIcon(task.status)}
        </TouchableOpacity>

        <View style={cardStyles.taskInfo}>
          <View style={cardStyles.taskTitleRow}>
            <Text style={[
              cardStyles.taskTitle,
              task.status === 'completed' && cardStyles.completedTitle
            ]}>
              {task.title}
            </Text>
            {task.is_milestone && (
              <View style={cardStyles.milestoneTag}>
                <Star size={localScale(12)} color={theme.colors.status.warning} />
              </View>
            )}
          </View>

          {task.description && (
            <Text style={[
              cardStyles.taskDescription,
              task.status === 'completed' && cardStyles.completedDescription
            ]}>
              {task.description}
            </Text>
          )}

          {/* Tags */}
          {task.tags.length > 0 && (
            <View style={cardStyles.tagsContainer}>
              {task.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={cardStyles.tag}>
                  <Text style={cardStyles.tagText}>{tag}</Text>
                </View>
              ))}
              {task.tags.length > 3 && (
                <Text style={cardStyles.moreTagsText}>+{task.tags.length - 3}</Text>
              )}
            </View>
          )}

          {/* Subject */}
          {subject && (
            <View style={cardStyles.subjectTag}>
              <View style={[cardStyles.subjectDot, { backgroundColor: subject.color }]} />
              <Text style={cardStyles.subjectName}>{subject.name}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity onPress={onEdit} style={cardStyles.editButton}>
          <MoreVertical size={localScale(16)} color={theme.colors.text.secondary} />
        </TouchableOpacity>
      </View>

      {/* Enhanced Progress Bar */}
      {task.progress_percentage > 0 && (
        <View style={cardStyles.progressContainer}>
          <View style={cardStyles.progressHeader}>
            <Text style={cardStyles.progressLabel}>Progress</Text>
            <View style={cardStyles.progressBadge}>
              <Text style={cardStyles.progressText}>{task.progress_percentage}%</Text>
            </View>
          </View>
          <View style={cardStyles.progressBar}>
            <Animated.View
              style={[
                cardStyles.progressFill,
                {
                  width: `${task.progress_percentage}%`,
                  backgroundColor: getPriorityColor(task.priority)
                }
              ]}
            />
            {task.progress_percentage >= 100 && (
              <View style={cardStyles.completionIndicator}>
                <CheckCircle size={localScale(12)} color={theme.colors.status.success} />
              </View>
            )}
          </View>
          {task.is_milestone && (
            <View style={cardStyles.milestoneIndicator}>
              <Award size={localScale(14)} color={theme.colors.status.warning} />
              <Text style={cardStyles.milestoneText}>Milestone</Text>
            </View>
          )}

          {/* Interactive Progress Slider */}
          {task.status !== 'completed' && task.status !== 'cancelled' && (
            <View style={cardStyles.progressSliderContainer}>
              <Text style={cardStyles.sliderLabel}>Update Progress</Text>
              <View style={cardStyles.progressSlider}>
                <TouchableOpacity
                  style={[cardStyles.progressSliderTrack, { width: '100%' }]}
                  onPress={(event) => {
                    const { locationX } = event.nativeEvent;
                    const sliderWidth = screenWidth - (2 * localScale(16)); // Approximate slider width
                    const newProgress = Math.round((locationX / sliderWidth) * 100);
                    const clampedProgress = Math.max(0, Math.min(100, newProgress));
                    onUpdateProgress(clampedProgress);
                  }}
                >
                  <View style={[cardStyles.progressSliderFill, { width: `${task.progress_percentage}%` }]} />
                  <View
                    style={[
                      cardStyles.progressSliderThumb,
                      { left: `${task.progress_percentage}%` }
                    ]}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      )}

      {/* Task Meta */}
      <View style={cardStyles.taskMeta}>
        <View style={cardStyles.taskMetaLeft}>
          {/* Priority */}
          <View style={cardStyles.priorityTag}>
            {getPriorityIcon(task.priority)}
            <Text style={[cardStyles.priorityText, { color: getPriorityColor(task.priority) }]}>
              {task.priority}
            </Text>
          </View>
        </View>

        <View style={cardStyles.taskMetaRight}>
          {/* Due Date */}
          {task.due_date && (
            <View style={cardStyles.dueDateContainer}>
              <CalendarIcon size={localScale(14)} color={isTaskOverdue ? theme.colors.status.error : theme.colors.text.secondary} />
              <Text style={[
                cardStyles.dueDateText,
                isTaskOverdue && cardStyles.overdueDateText
              ]}>
                {formatDate(task.due_date)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = (screenWidth: number, theme: any) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    taskCard: {
      backgroundColor: theme.colors.background.card,
      borderRadius: localScale(16),
      padding: localScale(16),
      marginBottom: localScale(12),
      shadowColor: theme.colors.ui.shadow,
      shadowOffset: { width: 0, height: localScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: localScale(4),
      elevation: localScale(3),
    },
    overdueCard: {
      borderLeftWidth: localScale(4),
      borderLeftColor: theme.colors.status.error,
    },
    completedCard: {
      opacity: 0.7,
    },
    taskHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: localScale(12),
    },
    statusButton: {
      marginRight: localScale(12),
      marginTop: localScale(2),
    },
    taskInfo: {
      flex: 1,
    },
    taskTitleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(4),
    },
    taskTitle: {
      fontSize: localScale(16),
      fontWeight: '600',
      color: theme.colors.text.primary,
      flex: 1,
    },
    completedTitle: {
      textDecorationLine: 'line-through',
      color: theme.colors.text.secondary,
    },
    milestoneTag: {
      marginLeft: localScale(8),
    },
    taskDescription: {
      fontSize: localScale(14),
      color: theme.colors.text.secondary,
      marginBottom: localScale(8),
      lineHeight: localScale(20),
    },
    completedDescription: {
      color: theme.colors.text.disabled,
    },
    tagsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(8),
    },
    tag: {
      backgroundColor: theme.colors.background.secondary,
      paddingHorizontal: localScale(8),
      paddingVertical: localScale(4),
      borderRadius: localScale(12),
      marginRight: localScale(6),
    },
    tagText: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
      fontWeight: '500',
    },
    moreTagsText: {
      fontSize: localScale(12),
      color: theme.colors.text.disabled,
      fontStyle: 'italic',
    },
    subjectTag: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(8),
    },
    subjectDot: {
      width: localScale(8),
      height: localScale(8),
      borderRadius: localScale(4),
      marginRight: localScale(6),
    },
    subjectName: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
      fontWeight: '500',
    },
    editButton: {
      padding: localScale(4),
    },
    progressContainer: {
      marginBottom: localScale(12),
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: localScale(8),
    },
    progressLabel: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
      fontWeight: '500',
    },
    progressBadge: {
      backgroundColor: theme.colors.background.secondary,
      paddingHorizontal: localScale(8),
      paddingVertical: localScale(2),
      borderRadius: localScale(8),
    },
    progressText: {
      fontSize: localScale(12),
      color: theme.colors.text.primary,
      fontWeight: '600',
    },
    progressBar: {
      height: localScale(6),
      backgroundColor: theme.colors.background.secondary,
      borderRadius: localScale(3),
      overflow: 'hidden',
      position: 'relative',
    },
    progressFill: {
      height: '100%',
      borderRadius: localScale(3),
    },
    completionIndicator: {
      position: 'absolute',
      right: localScale(4),
      top: localScale(-3),
    },
    milestoneIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: localScale(8),
      gap: localScale(4),
    },
    milestoneText: {
      fontSize: localScale(12),
      color: theme.colors.status.warning,
      fontWeight: '500',
    },
    progressSliderContainer: {
      marginTop: localScale(12),
    },
    sliderLabel: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
      marginBottom: localScale(8),
    },
    progressSlider: {
      alignItems: 'center',
    },
    progressSliderTrack: {
      height: localScale(20),
      backgroundColor: theme.colors.background.secondary,
      borderRadius: localScale(10),
      justifyContent: 'center',
      position: 'relative',
    },
    progressSliderFill: {
      height: localScale(6),
      backgroundColor: theme.colors.accent.primary,
      borderRadius: localScale(3),
      position: 'absolute',
      left: 0,
    },
    progressSliderThumb: {
      width: localScale(16),
      height: localScale(16),
      backgroundColor: theme.colors.accent.primary,
      borderRadius: localScale(8),
      position: 'absolute',
      marginLeft: localScale(-8),
    },
    taskMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    taskMetaLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(4),
    },
    priorityTag: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(4),
    },
    priorityText: {
      fontSize: localScale(12),
      fontWeight: '500',
      textTransform: 'capitalize',
    },
    taskMetaRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dueDateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(4),
    },
    dueDateText: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
    },
    overdueDateText: {
      color: theme.colors.status.error,
      fontWeight: '600',
    },
  });
};
