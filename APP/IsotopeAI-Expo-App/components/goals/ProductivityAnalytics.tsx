import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import {
  Activity,
  Clock,
  TrendingUp,
} from 'lucide-react-native';
import { Task } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';

interface ProductivityAnalyticsProps {
  tasks: Task[];
  screenWidth: number;
}

const ProductivityAnalytics: React.FC<ProductivityAnalyticsProps> = ({ tasks, screenWidth }) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  const { theme } = useTheme();
  // Calculate productivity metrics
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const totalTasks = tasks.length;
  const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;

  // Calculate average completion time
  const tasksWithDuration = completedTasks.filter(task => task.actual_duration);
  const avgCompletionTime = tasksWithDuration.length > 0
    ? tasksWithDuration.reduce((sum, task) => sum + (task.actual_duration || 0), 0) / tasksWithDuration.length
    : 0;

  // Calculate productivity by priority
  const priorityStats = ['urgent', 'high', 'medium', 'low'].map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const priorityCompleted = priorityTasks.filter(task => task.status === 'completed');
    const rate = priorityTasks.length > 0 ? (priorityCompleted.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      total: priorityTasks.length,
      completed: priorityCompleted.length,
      rate,
      color: priority === 'urgent' ? '#EF4444' :
             priority === 'high' ? '#F59E0B' :
             priority === 'medium' ? '#3B82F6' : '#10B981'
    };
  });

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  return (
    <View style={styles(screenWidth, theme).productivityAnalytics}>
      <Text style={styles(screenWidth, theme).analyticsTitle}>Productivity Analytics</Text>

      {/* Overall Stats */}
      <View style={styles(screenWidth, theme).analyticsOverview}>
        <View style={styles(screenWidth, theme).analyticsCard}>
          <Activity size={localScale(20)} color="#3B82F6" />
          <Text style={styles(screenWidth, theme).analyticsValue}>{Math.round(completionRate)}%</Text>
          <Text style={styles(screenWidth, theme).analyticsLabel}>Completion Rate</Text>
        </View>
        <View style={styles(screenWidth, theme).analyticsCard}>
          <Clock size={localScale(20)} color="#10B981" />
          <Text style={styles(screenWidth, theme).analyticsValue}>{formatDuration(avgCompletionTime)}</Text>
          <Text style={styles(screenWidth, theme).analyticsLabel}>Avg. Time</Text>
        </View>
        <View style={styles(screenWidth, theme).analyticsCard}>
          <TrendingUp size={localScale(20)} color="#F59E0B" />
          <Text style={styles(screenWidth, theme).analyticsValue}>{completedTasks.length}</Text>
          <Text style={styles(screenWidth, theme).analyticsLabel}>Completed</Text>
        </View>
      </View>

      {/* Priority Breakdown */}
      <View style={styles(screenWidth, theme).priorityBreakdown}>
        <Text style={styles(screenWidth, theme).breakdownTitle}>Progress by Priority</Text>
        {priorityStats.map((stat) => (
          <View key={stat.priority} style={styles(screenWidth, theme).priorityRow}>
            <View style={styles(screenWidth, theme).priorityInfo}>
              <Text style={styles(screenWidth, theme).priorityName}>
                {stat.priority.charAt(0).toUpperCase() + stat.priority.slice(1)}
              </Text>
            </View>
            <View style={styles(screenWidth, theme).priorityStats}>
              <View style={styles(screenWidth, theme).progressBarTrack}>
                <View style={[styles(screenWidth, theme).progressBarFill, { width: `${stat.rate}%` }]} />
              </View>
              <Text style={styles(screenWidth, theme).priorityCount}>{stat.completed}/{stat.total} tasks</Text>
              <Text style={[styles(screenWidth, theme).priorityRate, { color: stat.color }]}>
                {Math.round(stat.rate)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = (screenWidth: number, theme: any) => { // Pass theme as a parameter
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    productivityAnalytics: {
      backgroundColor: theme.colors.background.card,
      borderRadius: localScale(16),
      padding: localScale(20),
      marginHorizontal: localScale(16),
      marginBottom: localScale(16),
      ...theme.shadows.md,
    },
    analyticsTitle: {
      fontSize: localScale(18),
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: localScale(16),
    },
    analyticsOverview: {
      flexDirection: 'row',
      gap: localScale(12),
      marginBottom: localScale(20),
    },
    analyticsCard: {
      flex: 1,
      backgroundColor: theme.colors.background.secondary,
      borderRadius: localScale(12),
      padding: localScale(16),
      alignItems: 'center',
      gap: localScale(8),
    },
    analyticsValue: {
      fontSize: localScale(20),
      fontWeight: '700',
      color: theme.colors.text.primary,
    },
    analyticsLabel: {
      fontSize: localScale(12),
      color: theme.colors.text.secondary,
      textAlign: 'center',
    },
    priorityBreakdown: {
      gap: localScale(12),
    },
    breakdownTitle: {
      fontSize: localScale(16),
      fontWeight: '600',
      color: theme.colors.text.primary,
      marginBottom: localScale(8),
    },
    priorityRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: localScale(8),
    },
    priorityInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(8),
    },
    priorityDot: {
      width: localScale(8),
      height: localScale(8),
      borderRadius: localScale(4),
    },
    priorityName: {
      fontSize: localScale(14),
      color: theme.colors.text.primary,
      fontWeight: '500',
    },
    priorityStats: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(12),
    },
    priorityCount: {
      fontSize: localScale(14),
      color: theme.colors.text.secondary,
    },
    priorityRate: {
      fontSize: localScale(14),
      fontWeight: '600',
    },
    progressBarTrack: {
      height: localScale(6),
      width: localScale(80), // Fixed width for the progress bar track
      backgroundColor: theme.colors.ui.progressBarTrack,
      borderRadius: localScale(3),
      overflow: 'hidden',
    },
    progressBarFill: {
      height: '100%',
      backgroundColor: theme.colors.ui.progressBarFill,
      borderRadius: localScale(3),
    },
  });
};

export default ProductivityAnalytics;
