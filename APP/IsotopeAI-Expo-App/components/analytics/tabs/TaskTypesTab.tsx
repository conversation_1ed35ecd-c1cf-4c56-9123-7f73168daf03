import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, ChartDataPoint } from '@/types/app';

const { width } = Dimensions.get('window');

interface TaskTypesTabProps {
  analytics: Analytics | null;
  taskTypeChartData: ChartDataPoint[];
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

const TASK_TYPE_ICONS: { [key: string]: keyof typeof MaterialIcons.glyphMap } = {
  'Study': 'menu-book',
  'Practice': 'fitness-center',
  'Review': 'refresh',
  'Research': 'search',
  'Assignment': 'assignment',
  'Project': 'work',
  'Reading': 'auto-stories',
  'Writing': 'edit',
  'Problem Solving': 'psychology',
  'Memorization': 'memory',
  'Other': 'more-horiz',
};

export function TaskTypesTab({ 
  analytics, 
  taskTypeChartData, 
  formatTime, 
  loading, 
  error 
}: TaskTypesTabProps) {
  
  const getTaskTypeIcon = (taskType: string): keyof typeof MaterialIcons.glyphMap => {
    return TASK_TYPE_ICONS[taskType] || 'category';
  };

  const renderTaskTypeChart = () => {
    if (!analytics?.taskTypeStats || analytics.taskTypeStats.length === 0) return null;

    const maxDuration = Math.max(...analytics.taskTypeStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Task Type Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.barChart}>
            {analytics.taskTypeStats.map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const colors = ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B', '#10B981', '#06B6D4', '#84CC16'];
              const color = colors[index % colors.length];
              
              return (
                <View key={stat.taskType} style={styles.barContainer}>
                  <View style={[styles.barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={[color, `${color}CC`]}
                      style={[
                        styles.bar,
                        { height: Math.max(height, 4) }
                      ]}
                    />
                  </View>
                  <Text style={styles.barLabel} numberOfLines={2}>
                    {stat.taskType}
                  </Text>
                  <Text style={styles.barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderPieChart = () => {
    if (!taskTypeChartData || taskTypeChartData.length === 0) return null;

    return (
      <View style={styles.pieContainer}>
        <Text style={styles.pieTitle}>Task Type Distribution</Text>
        
        {/* Pie chart representation */}
        <View style={styles.pieChart}>
          {taskTypeChartData.map((item, index) => (
            <View
              key={item.label}
              style={[
                styles.pieSlice,
                {
                  backgroundColor: item.color,
                  width: `${item.percentage || 0}%`,
                }
              ]}
            />
          ))}
        </View>

        {/* Legend */}
        <View style={styles.pieLegend}>
          {taskTypeChartData.map((item, index) => (
            <View key={item.label} style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: item.color }]} />
              <MaterialIcons 
                name={getTaskTypeIcon(item.label)} 
                size={16} 
                color={item.color} 
                style={styles.legendIcon}
              />
              <Text style={styles.legendLabel}>{item.label}</Text>
              <Text style={styles.legendValue}>
                {(item.percentage || 0).toFixed(1)}%
              </Text>
              <Text style={styles.legendTime}>
                ({formatTime(item.value * 60)})
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderTaskTypeList = () => {
    if (!analytics?.taskTypeStats || analytics.taskTypeStats.length === 0) return null;

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Task Type Details</Text>
        {analytics.taskTypeStats.map((stat, index) => {
          const averageSession = stat.sessionCount > 0 ? stat.totalDuration / stat.sessionCount : 0;
          const colors = ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B', '#10B981', '#06B6D4', '#84CC16'];
          const color = colors[index % colors.length];
          
          return (
            <View key={stat.taskType} style={styles.taskTypeItem}>
              <View style={styles.taskTypeHeader}>
                <View style={styles.taskTypeInfo}>
                  <View style={[styles.taskTypeIconContainer, { backgroundColor: `${color}20` }]}>
                    <MaterialIcons 
                      name={getTaskTypeIcon(stat.taskType)} 
                      size={20} 
                      color={color} 
                    />
                  </View>
                  <View style={styles.taskTypeDetails}>
                    <Text style={styles.taskTypeName}>{stat.taskType}</Text>
                    <Text style={styles.taskTypeRank}>#{index + 1} most used</Text>
                  </View>
                </View>
                <Text style={styles.taskTypeDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              <View style={styles.taskTypeStats}>
                <View style={styles.taskTypeStatItem}>
                  <MaterialIcons name="play-circle-outline" size={16} color="#64748B" />
                  <Text style={styles.taskTypeStatText}>
                    {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles.taskTypeStatItem}>
                  <MaterialIcons name="trending-up" size={16} color="#64748B" />
                  <Text style={styles.taskTypeStatText}>
                    {formatTime(averageSession)} avg
                  </Text>
                </View>
                
                {stat.averageProductivityRating > 0 && (
                  <View style={styles.taskTypeStatItem}>
                    <MaterialIcons name="star" size={16} color="#F59E0B" />
                    <Text style={styles.taskTypeStatText}>
                      {stat.averageProductivityRating.toFixed(1)} rating
                    </Text>
                  </View>
                )}
              </View>

              {/* Progress bar showing relative time */}
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <LinearGradient
                    colors={[color, `${color}CC`]}
                    style={[
                      styles.progressFill,
                      { width: `${stat.percentage}%` }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {stat.percentage.toFixed(1)}% of total study time
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderTaskTypeSummary = () => {
    if (!analytics?.taskTypeStats || analytics.taskTypeStats.length === 0) return null;

    const totalTaskTypes = analytics.taskTypeStats.length;
    const totalTime = analytics.taskTypeStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = analytics.taskTypeStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const topTaskType = analytics.taskTypeStats[0];
    const averageTimePerType = totalTaskTypes > 0 ? totalTime / totalTaskTypes : 0;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Task Type Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{totalTaskTypes}</Text>
            <Text style={styles.summaryLabel}>Task Types</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(averageTimePerType)}</Text>
            <Text style={styles.summaryLabel}>Avg/Type</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue} numberOfLines={1}>
              {topTaskType?.taskType || 'None'}
            </Text>
            <Text style={styles.summaryLabel}>Most Used</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>
              {topTaskType ? `${topTaskType.percentage.toFixed(0)}%` : '0%'}
            </Text>
            <Text style={styles.summaryLabel}>Top Share</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="category" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading task type analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!analytics?.taskTypeStats || analytics.taskTypeStats.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <MaterialIcons name="category" size={64} color="#94A3B8" />
        <Text style={styles.emptyTitle}>No Task Type Data</Text>
        <Text style={styles.emptyText}>
          Start studying with different task types to see your task type analytics here.
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderTaskTypeSummary()}
      {renderPieChart()}
      {renderTaskTypeChart()}
      {renderTaskTypeList()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    margin: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
    textAlign: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  pieContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pieTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  pieChart: {
    flexDirection: 'row',
    height: 12,
    backgroundColor: '#E2E8F0',
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 16,
  },
  pieSlice: {
    height: '100%',
  },
  pieLegend: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendIcon: {
    marginLeft: -4,
  },
  legendLabel: {
    flex: 1,
    fontSize: 14,
    color: '#1E293B',
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6366F1',
    minWidth: 40,
  },
  legendTime: {
    fontSize: 12,
    color: '#64748B',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 60,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 32,
    borderRadius: 16,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#64748B',
    marginBottom: 2,
    textAlign: 'center',
    width: 60,
  },
  barValue: {
    fontSize: 9,
    color: '#64748B',
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  taskTypeItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  taskTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  taskTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  taskTypeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  taskTypeDetails: {
    flex: 1,
  },
  taskTypeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 2,
  },
  taskTypeRank: {
    fontSize: 12,
    color: '#64748B',
  },
  taskTypeDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  taskTypeStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 12,
  },
  taskTypeStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  taskTypeStatText: {
    fontSize: 12,
    color: '#64748B',
  },
  progressContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingTop: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#E2E8F0',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginTop: 12,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#64748B',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#94A3B8',
    textAlign: 'center',
    lineHeight: 24,
  },
});
