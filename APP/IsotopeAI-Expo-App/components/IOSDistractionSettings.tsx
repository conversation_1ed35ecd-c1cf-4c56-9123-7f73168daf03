import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';

interface IOSDistractionSettingsProps {
  onClose?: () => void;
}

export function IOSDistractionSettings({ onClose }: IOSDistractionSettingsProps) {
  const {
    iosBlockingState,
    requestIOSPermissions,
    presentIOSAppSelector,
    getIOSCapabilities,
    blockedApps,
  } = useDistractionBlocking();

  const [isLoading, setIsLoading] = useState(false);

  const handleRequestPermissions = async () => {
    setIsLoading(true);
    try {
      const granted = await requestIOSPermissions();
      if (granted) {
        Alert.alert(
          'Permissions Granted',
          'Screen Time permissions have been granted. You can now select apps to block during focus sessions.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Failed to request permissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectApps = async () => {
    setIsLoading(true);
    try {
      const selectedApps = await presentIOSAppSelector();
      if (selectedApps.length > 0) {
        Alert.alert(
          'Apps Selected',
          `${selectedApps.length} apps have been added to your blocked list and will be blocked during focus sessions.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Failed to select apps:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const capabilities = getIOSCapabilities();
  const iosBlockedApps = blockedApps.filter(app => 
    app.packageName.includes('com.') && !app.packageName.includes('android')
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>iOS App Blocking</Text>
        <Text style={styles.subtitle}>
          Use Apple's Screen Time to block distracting apps during focus sessions
        </Text>
      </View>

      {/* Permission Status */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Ionicons 
            name={iosBlockingState.isAuthorized ? "checkmark-circle" : "alert-circle"} 
            size={24} 
            color={iosBlockingState.isAuthorized ? "#10B981" : "#F59E0B"} 
          />
          <Text style={styles.sectionTitle}>Screen Time Permission</Text>
        </View>
        
        <Text style={styles.sectionDescription}>
          {iosBlockingState.isAuthorized 
            ? "Screen Time permission is granted. You can block apps during focus sessions."
            : "Screen Time permission is required to block apps on iOS."
          }
        </Text>

        {!iosBlockingState.isAuthorized && (
          <TouchableOpacity 
            style={styles.primaryButton} 
            onPress={handleRequestPermissions}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <>
                <Ionicons name="shield-checkmark" size={20} color="#FFFFFF" />
                <Text style={styles.buttonText}>Grant Permission</Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* App Selection */}
      {iosBlockingState.isAuthorized && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Ionicons name="apps" size={24} color="#6366F1" />
            <Text style={styles.sectionTitle}>Select Apps to Block</Text>
          </View>
          
          <Text style={styles.sectionDescription}>
            Use Apple's native app selector to choose which apps to block during focus sessions.
          </Text>

          <TouchableOpacity 
            style={styles.secondaryButton} 
            onPress={handleSelectApps}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#6366F1" />
            ) : (
              <>
                <Ionicons name="add-circle" size={20} color="#6366F1" />
                <Text style={styles.secondaryButtonText}>Select Apps</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Currently Blocked Apps */}
      {iosBlockedApps.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Ionicons name="ban" size={24} color="#EF4444" />
            <Text style={styles.sectionTitle}>Blocked Apps ({iosBlockedApps.length})</Text>
          </View>
          
          <Text style={styles.sectionDescription}>
            These apps will be blocked during focus sessions:
          </Text>

          <View style={styles.appsList}>
            {iosBlockedApps.map((app) => (
              <View key={app.id} style={styles.appItem}>
                <Text style={styles.appIcon}>{app.icon}</Text>
                <View style={styles.appInfo}>
                  <Text style={styles.appName}>{app.name}</Text>
                  <Text style={styles.appCategory}>{app.category}</Text>
                </View>
                <View style={styles.appStatus}>
                  <Ionicons name="ban" size={16} color="#EF4444" />
                </View>
              </View>
            ))}
          </View>
        </View>
      )}

      {onClose && (
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Done</Text>
        </TouchableOpacity>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 12,
    padding: 20,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 12,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: '#7C3AED',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  secondaryButton: {
    backgroundColor: '#F3F4F6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    gap: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#7C3AED',
    fontSize: 16,
    fontWeight: '600',
  },
  appsList: {
    gap: 12,
  },
  appItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  appIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  appInfo: {
    flex: 1,
  },
  appName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  appCategory: {
    fontSize: 12,
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  appStatus: {
    padding: 4,
  },
  capabilitiesList: {
    gap: 8,
  },
  capabilityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  capabilityText: {
    fontSize: 14,
    color: '#374151',
  },
  closeButton: {
    backgroundColor: '#6366F1',
    marginHorizontal: 20,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
