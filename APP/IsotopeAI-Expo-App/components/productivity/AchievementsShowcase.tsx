import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Trophy,
  Award,
  Star,
  Lock,
  Flame,
  Clock,
  BookOpen,
  Target,
  X,
  TrendingUp,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/hooks/useAuth';
import { productivityFeaturesService, Achievement } from '@/services/productivityFeaturesService';
import { Card } from '@/components/ui';

const { width: screenWidth } = Dimensions.get('window');

interface AchievementsShowcaseProps {
  visible: boolean;
  onClose: () => void;
}

export const AchievementsShowcase: React.FC<AchievementsShowcaseProps> = ({
  visible,
  onClose,
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<Achievement['category'] | 'all'>('all');
  const [loading, setLoading] = useState(true);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  const categories: { key: Achievement['category'] | 'all'; label: string; icon: any }[] = [
    { key: 'all', label: 'All', icon: Trophy },
    { key: 'streak', label: 'Streaks', icon: Flame },
    { key: 'time', label: 'Time', icon: Clock },
    { key: 'session', label: 'Sessions', icon: BookOpen },
    { key: 'subject', label: 'Subjects', icon: Star },
    { key: 'milestone', label: 'Milestones', icon: Target },
  ];

  useEffect(() => {
    if (visible && user) {
      loadAchievements();
      
      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      slideAnim.setValue(50);
    }
  }, [visible, user]);

  const loadAchievements = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Check for new achievements first
      await productivityFeaturesService.checkAndUpdateAchievements(user.id);
      
      // Load all achievements
      const userAchievements = await productivityFeaturesService.getUserAchievements(user.id);
      setAchievements(userAchievements);
    } catch (error) {
      console.error('Error loading achievements:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredAchievements = (): Achievement[] => {
    if (selectedCategory === 'all') {
      return achievements;
    }
    return productivityFeaturesService.getAchievementsByCategory(achievements, selectedCategory);
  };

  const getUnlockedCount = (): number => {
    return productivityFeaturesService.getUnlockedAchievements(achievements).length;
  };

  const getTotalCount = (): number => {
    return achievements.length;
  };

  const getCategoryIcon = (category: Achievement['category']) => {
    switch (category) {
      case 'streak': return Flame;
      case 'time': return Clock;
      case 'session': return BookOpen;
      case 'subject': return Star;
      case 'milestone': return Target;
      default: return Trophy;
    }
  };

  const getCategoryColor = (category: Achievement['category']): string => {
    switch (category) {
      case 'streak': return '#FF6B35';
      case 'time': return '#4F46E5';
      case 'session': return '#059669';
      case 'subject': return '#DC2626';
      case 'milestone': return '#7C3AED';
      default: return theme.colors.accent.primary;
    }
  };

  const formatProgress = (achievement: Achievement): string => {
    if (achievement.unlocked) return 'Completed';
    
    const percentage = productivityFeaturesService.getProgressPercentage(achievement);
    return `${achievement.progress}/${achievement.maxProgress} (${Math.round(percentage)}%)`;
  };

  const renderAchievementCard = (achievement: Achievement) => {
    const IconComponent = getCategoryIcon(achievement.category);
    const categoryColor = getCategoryColor(achievement.category);
    const isUnlocked = achievement.unlocked;
    const progress = productivityFeaturesService.getProgressPercentage(achievement);

    return (
      <Card
        key={achievement.id}
        style={[
          styles.achievementCard,
          {
            backgroundColor: theme.colors.background.card,
            opacity: isUnlocked ? 1 : 0.7,
          },
        ]}
      >
        {/* Achievement Icon */}
        <View style={styles.achievementHeader}>
          <View
            style={[
              styles.achievementIconContainer,
              {
                backgroundColor: isUnlocked ? categoryColor + '20' : theme.colors.background.secondary,
              },
            ]}
          >
            {isUnlocked ? (
              <Text style={styles.achievementEmoji}>{achievement.icon}</Text>
            ) : (
              <Lock size={24} color={theme.colors.text.tertiary} />
            )}
          </View>
          
          {isUnlocked && (
            <View style={[styles.unlockedBadge, { backgroundColor: categoryColor }]}>
              <Award size={12} color={theme.colors.text.inverse} />
            </View>
          )}
        </View>

        {/* Achievement Info */}
        <View style={styles.achievementInfo}>
          <Text
            style={[
              styles.achievementTitle,
              {
                color: isUnlocked ? theme.colors.text.primary : theme.colors.text.secondary,
              },
            ]}
          >
            {achievement.title}
          </Text>
          <Text
            style={[
              styles.achievementDescription,
              {
                color: isUnlocked ? theme.colors.text.secondary : theme.colors.text.tertiary,
              },
            ]}
          >
            {achievement.description}
          </Text>
          
          {/* Progress */}
          <View style={styles.progressContainer}>
            <Text
              style={[
                styles.progressText,
                {
                  color: isUnlocked ? categoryColor : theme.colors.text.tertiary,
                },
              ]}
            >
              {formatProgress(achievement)}
            </Text>
            
            {!isUnlocked && (
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressBarBg,
                    { backgroundColor: theme.colors.background.secondary },
                  ]}
                >
                  <View
                    style={[
                      styles.progressBarFill,
                      {
                        width: `${progress}%`,
                        backgroundColor: categoryColor,
                      },
                    ]}
                  />
                </View>
              </View>
            )}
          </View>

          {/* Unlock Date */}
          {isUnlocked && achievement.unlockedAt && (
            <Text style={[styles.unlockDate, { color: theme.colors.text.tertiary }]}>
              Unlocked {achievement.unlockedAt.toLocaleDateString()}
            </Text>
          )}
        </View>

        {/* Category Badge */}
        <View style={styles.categoryBadge}>
          <IconComponent size={12} color={categoryColor} />
          <Text style={[styles.categoryText, { color: categoryColor }]}>
            {achievement.category.charAt(0).toUpperCase() + achievement.category.slice(1)}
          </Text>
        </View>
      </Card>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              backgroundColor: theme.colors.background.primary,
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFA500']}
                style={styles.headerIcon}
              >
                <Trophy size={24} color={theme.colors.text.inverse} />
              </LinearGradient>
              <View>
                <Text style={[styles.title, { color: theme.colors.text.primary }]}>
                  Achievements
                </Text>
                <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
                  {getUnlockedCount()} of {getTotalCount()} unlocked
                </Text>
              </View>
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>

          {/* Progress Overview */}
          <Card style={[styles.overviewCard, { backgroundColor: theme.colors.background.card }]}>
            <View style={styles.overviewContent}>
              <View style={styles.overviewStat}>
                <Text style={[styles.overviewNumber, { color: theme.colors.text.primary }]}>
                  {getUnlockedCount()}
                </Text>
                <Text style={[styles.overviewLabel, { color: theme.colors.text.secondary }]}>
                  Unlocked
                </Text>
              </View>
              <View style={styles.overviewDivider} />
              <View style={styles.overviewStat}>
                <Text style={[styles.overviewNumber, { color: theme.colors.text.primary }]}>
                  {Math.round((getUnlockedCount() / getTotalCount()) * 100)}%
                </Text>
                <Text style={[styles.overviewLabel, { color: theme.colors.text.secondary }]}>
                  Complete
                </Text>
              </View>
              <View style={styles.overviewDivider} />
              <View style={styles.overviewStat}>
                <TrendingUp size={20} color={theme.colors.accent.primary} />
                <Text style={[styles.overviewLabel, { color: theme.colors.text.secondary }]}>
                  Progress
                </Text>
              </View>
            </View>
          </Card>

          {/* Category Filter */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoryFilter}
            contentContainerStyle={styles.categoryFilterContent}
          >
            {categories.map((category) => {
              const IconComponent = category.icon;
              const isSelected = selectedCategory === category.key;
              
              return (
                <TouchableOpacity
                  key={category.key}
                  style={[
                    styles.categoryButton,
                    {
                      backgroundColor: isSelected
                        ? theme.colors.accent.primary
                        : theme.colors.background.card,
                      borderColor: isSelected
                        ? theme.colors.accent.primary
                        : theme.colors.border.primary,
                    },
                  ]}
                  onPress={() => setSelectedCategory(category.key)}
                >
                  <IconComponent
                    size={16}
                    color={isSelected ? theme.colors.text.inverse : theme.colors.text.secondary}
                  />
                  <Text
                    style={[
                      styles.categoryButtonText,
                      {
                        color: isSelected ? theme.colors.text.inverse : theme.colors.text.secondary,
                      },
                    ]}
                  >
                    {category.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          {/* Achievements Grid */}
          <ScrollView
            style={styles.achievementsContainer}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.achievementsGrid}>
              {getFilteredAchievements().map(renderAchievementCard)}
            </View>
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = {
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: screenWidth * 0.95,
    height: '90%',
    borderRadius: 24,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 22,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  closeButton: {
    padding: 8,
  },
  overviewCard: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 20,
  },
  overviewContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  overviewStat: {
    alignItems: 'center',
    gap: 4,
  },
  overviewNumber: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  overviewLabel: {
    fontSize: 12,
  },
  overviewDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E7EB',
  },
  categoryFilter: {
    marginBottom: 20,
  },
  categoryFilterContent: {
    paddingHorizontal: 4,
    gap: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  categoryButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  achievementsContainer: {
    flex: 1,
  },
  achievementsGrid: {
    gap: 12,
  },
  achievementCard: {
    padding: 16,
    borderRadius: 16,
    position: 'relative',
  },
  achievementHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  achievementIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementEmoji: {
    fontSize: 24,
  },
  unlockedBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  progressBar: {
    marginTop: 4,
  },
  progressBarBg: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  unlockDate: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  categoryBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  categoryText: {
    fontSize: 10,
    fontWeight: '500',
  },
};
