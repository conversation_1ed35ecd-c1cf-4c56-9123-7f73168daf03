import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { TimerSession, SessionSummary, TimerMode, TaskType } from '@/types/app';
import { useTheme } from '@/hooks/useTheme.ts';
import { Button, Modal, Card } from '@/components/ui';
import { useSessionStore } from '@/stores/sessionStore';
import { SessionFeedbackCard } from './SessionFeedbackCard';

interface SessionCompletionFlowProps {
  visible: boolean;
  onClose: () => void;
  session: TimerSession;
  userId: string;
  completionType: 'complete' | 'pause' | 'cancel';
  onSessionSaved?: (session: TimerSession) => void;
}

export function SessionCompletionFlow({
  visible,
  onClose,
  session,
  userId,
  completionType,
  onSessionSaved,
}: SessionCompletionFlowProps) {
  const theme = useTheme();
  const { endSession, updateSession, isLoading } = useSessionStore();
  
  const [currentStep, setCurrentStep] = useState<'summary' | 'feedback' | 'complete'>('summary');
  const [sessionSummary, setSessionSummary] = useState<SessionSummary>({
    duration: 0,
    subject: '',
    taskName: '',
    taskType: 'General Study', // Default or a more appropriate initial value
    mode: 'stopwatch',
    phase: 'work',
    completed: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    if (visible) {
      setCurrentStep('summary');
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(0);
      slideAnim.setValue(50);
    }
  }, [visible]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getCompletionTitle = (): string => {
    switch (completionType) {
      case 'complete':
        return 'Session Complete! 🎉';
      case 'pause':
        return 'Session Paused ⏸️';
      case 'cancel':
        return 'Session Cancelled ❌';
      default:
        return 'Session Ended';
    }
  };

  const getCompletionMessage = (): string => {
    switch (completionType) {
      case 'complete':
        return 'Great work! You\'ve completed your study session.';
      case 'pause':
        return 'Your session has been paused. You can resume it later.';
      case 'cancel':
        return 'Your session has been cancelled.';
      default:
        return 'Your session has ended.';
    }
  };

  const getSessionTypeLabel = (): string => {
    if (session.mode === 'pomodoro') {
      switch (session.phase) {
        case 'work':
          return 'Pomodoro Work Session';
        case 'shortBreak':
          return 'Short Break';
        case 'longBreak':
          return 'Long Break';
        default:
          return 'Pomodoro Session';
      }
    }
    return 'Study Session';
  };

  const handleContinueToFeedback = () => {
    setCurrentStep('feedback');
  };

  const handleSkipFeedback = async () => {
    await handleSessionSave({
      duration: session.duration,
      subject: session.subject || '',
      taskName: session.taskName || '',
      taskType: session.taskType || 'General Study',
      mode: session.mode,
      phase: session.phase || 'work',
      completed: session.completed,
      productivityRating: session.productivityRating,
      feedback: session.feedback,
      notes: session.notes,
    });
  };

  const handleFeedbackSubmit = async (summary: SessionSummary) => {
    await handleSessionSave(summary);
  };

  const handleSessionSave = async (summary: SessionSummary) => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      let updatedSession: TimerSession;
      
      if (completionType === 'complete') {
        // Complete the session with summary
        updatedSession = await endSession(session.id, summary as SessionSummary);
      } else if (completionType === 'pause') {
        // Update session with summary but don't mark as completed
        updatedSession = await updateSession(session.id, {
          notes: summary.notes,
          feedback: summary.feedback,
          productivityRating: summary.productivityRating,
          taskName: summary.taskName,
          taskType: summary.taskType as TimerSession['taskType'],
        });
      } else {
        // Cancel session
        updatedSession = await updateSession(session.id, {
          completed: false,
          endTime: new Date(),
          notes: 'Session cancelled',
        });
      }

      setSessionSummary(summary);
      setCurrentStep('complete');
      
      if (onSessionSaved) {
        onSessionSaved(updatedSession);
      }
      
      // Auto-close after showing completion
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save session');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderSummaryStep = () => (
    <Animated.View 
      style={[
        styles.stepContainer,
        { 
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <Card
        variant="elevated"
        style={StyleSheet.flatten([
          styles.summaryCard,
          { 
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          }
        ])}
      >
        {/* Header */}
        <View style={styles.summaryHeader}>
          <MaterialIcons 
            name={completionType === 'complete' ? 'check-circle' : completionType === 'pause' ? 'pause-circle' : 'cancel'} 
            size={32} 
            color={completionType === 'complete' ? '#10B981' : completionType === 'pause' ? '#F59E0B' : '#EF4444'} 
            style={styles.summaryIcon}
          />
          <View style={styles.summaryHeaderText}>
            <Text style={[styles.summaryTitle, { color: theme.colors.text.primary }]}>
              {getCompletionTitle()}
            </Text>
            <Text style={[styles.summarySubtitle, { color: theme.colors.text.secondary }]}>
              {getCompletionMessage()}
            </Text>
          </View>
        </View>

        {/* Session Details */}
        <View style={styles.summaryContent}>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
              Duration:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
              {formatDuration(session.duration)}
            </Text>
          </View>
          
          {session.subject && (
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
                Subject:
              </Text>
              <View style={styles.subjectContainer}>
                {session.subjectColor && (
                  <View 
                    style={[
                      styles.subjectColorDot,
                      { backgroundColor: session.subjectColor }
                    ]} 
                  />
                )}
                <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
                  {session.subject}
                </Text>
              </View>
            </View>
          )}
          
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: theme.colors.text.secondary }]}>
              Type:
            </Text>
            <Text style={[styles.summaryValue, { color: theme.colors.text.primary }]}>
              {getSessionTypeLabel()}
            </Text>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.summaryActions}>
          {completionType !== 'cancel' && (
            <Button
              title="Add Feedback"
              onPress={handleContinueToFeedback}
              variant="primary"
              size="lg"
              style={StyleSheet.flatten([
                styles.actionButton,
                { backgroundColor: theme.colors.primary }
              ])}
              textStyle={{ color: theme.colors.background }}
              icon={
                <MaterialIcons 
                  name="feedback" 
                  size={20} 
                  color={theme.colors.background} 
                />
              }
              iconPosition="right"
            />
          )}
          <Button
            title={completionType === 'cancel' ? "Close" : "Skip Feedback"}
            onPress={handleSkipFeedback}
            variant="outline"
            size="lg"
            style={StyleSheet.flatten([
              styles.actionButton,
              { 
                borderColor: theme.colors.border,
                backgroundColor: theme.colors.background,
              }
            ])}
            textStyle={{ color: theme.colors.text.secondary }}
            loading={isSubmitting}
            disabled={isSubmitting}
          />
        </View>
      </Card>
    </Animated.View>
  );

  const renderFeedbackStep = () => (
    <SessionFeedbackCard
      onSubmit={handleFeedbackSubmit}
      onSkip={handleSkipFeedback}
      initialTaskName={session.taskName}
      initialTaskType={session.taskType as TaskType | undefined}
      isLoading={isSubmitting}
      title="Session Feedback"
      subtitle="Help us understand how your session went"
    />
  );

  const renderCompleteStep = () => (
    <Animated.View 
      style={[
        styles.stepContainer,
        { 
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      <Card
        variant="elevated"
        style={StyleSheet.flatten([
          styles.completeCard,
          { 
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          }
        ])}
      >
        <MaterialIcons 
          name="check-circle" 
          size={64} 
          color="#10B981" 
          style={styles.completeIcon}
        />
        <Text style={[styles.completeTitle, { color: theme.colors.text.primary }]}>
          Session Saved!
        </Text>
        <Text style={[styles.completeSubtitle, { color: theme.colors.text.secondary }]}>
          Your session data has been saved successfully.
        </Text>
      </Card>
    </Animated.View>
  );

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      size="lg"
      style={{ backgroundColor: theme.colors.background }}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {currentStep === 'summary' && renderSummaryStep()}
        {currentStep === 'feedback' && renderFeedbackStep()}
        {currentStep === 'complete' && renderCompleteStep()}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  summaryCard: {
    borderRadius: 16,
    padding: 24,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  summaryIcon: {
    marginRight: 16,
  },
  summaryHeaderText: {
    flex: 1,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  summarySubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  summaryContent: {
    marginBottom: 24,
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  subjectContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subjectColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  summaryActions: {
    gap: 12,
  },
  actionButton: {
    borderRadius: 12,
  },
  completeCard: {
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
  },
  completeIcon: {
    marginBottom: 16,
  },
  completeTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  completeSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
