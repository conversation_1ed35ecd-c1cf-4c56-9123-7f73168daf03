import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { TimerSession } from '@/types/app';
import { useTheme } from '@/hooks/useTheme';
import { Card } from '@/components/ui';

interface SessionStatsCardProps {
  sessions: TimerSession[];
  title?: string;
  showDetailedStats?: boolean;
  style?: any;
}

interface SessionStats {
  totalSessions: number;
  totalDuration: number;
  averageDuration: number;
  completedSessions: number;
  completionRate: number;
  averageProductivityRating: number;
  topSubjects: Array<{ subject: string; duration: number; sessions: number; color?: string }>;
  topTaskTypes: Array<{ taskType: string; duration: number; sessions: number }>;
  longestSession: number;
  shortestSession: number;
  totalPomodoroSessions: number;
  totalStopwatchSessions: number;
}

export function SessionStatsCard({
  sessions,
  title = 'Session Statistics',
  showDetailedStats = true,
  style,
}: SessionStatsCardProps) {
  const theme = useTheme();

  const stats: SessionStats = useMemo(() => {
    if (sessions.length === 0) {
      return {
        totalSessions: 0,
        totalDuration: 0,
        averageDuration: 0,
        completedSessions: 0,
        completionRate: 0,
        averageProductivityRating: 0,
        topSubjects: [],
        topTaskTypes: [],
        longestSession: 0,
        shortestSession: 0,
        totalPomodoroSessions: 0,
        totalStopwatchSessions: 0,
      };
    }

    const totalSessions = sessions.length;
    const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);
    const completedSessions = sessions.filter(session => session.completed).length;
    const completionRate = (completedSessions / totalSessions) * 100;

    // Calculate average productivity rating
    const ratingsSum = sessions
      .filter(session => session.productivityRating)
      .reduce((sum, session) => sum + (session.productivityRating || 0), 0);
    const ratingsCount = sessions.filter(session => session.productivityRating).length;
    const averageProductivityRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

    // Calculate subject statistics
    const subjectMap = new Map<string, { duration: number; sessions: number; color?: string }>();
    sessions.forEach(session => {
      if (session.subject) {
        const existing = subjectMap.get(session.subject) || { duration: 0, sessions: 0 };
        subjectMap.set(session.subject, {
          duration: existing.duration + session.duration,
          sessions: existing.sessions + 1,
          color: session.subjectColor || existing.color,
        });
      }
    });

    const topSubjects = Array.from(subjectMap.entries())
      .map(([subject, data]) => ({ subject, ...data }))
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5);

    // Calculate task type statistics
    const taskTypeMap = new Map<string, { duration: number; sessions: number }>();
    sessions.forEach(session => {
      if (session.taskType) {
        const existing = taskTypeMap.get(session.taskType) || { duration: 0, sessions: 0 };
        taskTypeMap.set(session.taskType, {
          duration: existing.duration + session.duration,
          sessions: existing.sessions + 1,
        });
      }
    });

    const topTaskTypes = Array.from(taskTypeMap.entries())
      .map(([taskType, data]) => ({ taskType, ...data }))
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5);

    // Calculate session duration extremes
    const durations = sessions.map(session => session.duration);
    const longestSession = Math.max(...durations);
    const shortestSession = Math.min(...durations);

    // Calculate mode statistics
    const totalPomodoroSessions = sessions.filter(session => session.mode === 'pomodoro').length;
    const totalStopwatchSessions = sessions.filter(session => session.mode === 'stopwatch').length;

    return {
      totalSessions,
      totalDuration,
      averageDuration: totalDuration / totalSessions,
      completedSessions,
      completionRate,
      averageProductivityRating,
      topSubjects,
      topTaskTypes,
      longestSession,
      shortestSession,
      totalPomodoroSessions,
      totalStopwatchSessions,
    };
  }, [sessions]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatPercentage = (value: number): string => {
    return `${Math.round(value)}%`;
  };

  const renderStatItem = (icon: string, label: string, value: string, color?: string) => (
    <View style={styles.statItem}>
      <MaterialIcons 
        name={icon as any} 
        size={20} 
        color={color || theme.colors.primary} 
        style={styles.statIcon}
      />
      <View style={styles.statContent}>
        <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
          {value}
        </Text>
        <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>
          {label}
        </Text>
      </View>
    </View>
  );

  const renderTopSubjects = () => {
    if (stats.topSubjects.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          Top Subjects
        </Text>
        {stats.topSubjects.map((subject, index) => (
          <View key={subject.subject} style={styles.listItem}>
            <View style={styles.listItemLeft}>
              {subject.color && (
                <View 
                  style={[
                    styles.subjectColorDot,
                    { backgroundColor: subject.color }
                  ]} 
                />
              )}
              <Text style={[styles.listItemTitle, { color: theme.colors.text.primary }]}>
                {subject.subject}
              </Text>
            </View>
            <View style={styles.listItemRight}>
              <Text style={[styles.listItemValue, { color: theme.colors.text.primary }]}>
                {formatDuration(subject.duration)}
              </Text>
              <Text style={[styles.listItemSubvalue, { color: theme.colors.text.secondary }]}>
                {subject.sessions} sessions
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderTopTaskTypes = () => {
    if (stats.topTaskTypes.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
          Top Task Types
        </Text>
        {stats.topTaskTypes.map((taskType, index) => (
          <View key={taskType.taskType} style={styles.listItem}>
            <View style={styles.listItemLeft}>
              <Text style={[styles.listItemTitle, { color: theme.colors.text.primary }]}>
                {taskType.taskType}
              </Text>
            </View>
            <View style={styles.listItemRight}>
              <Text style={[styles.listItemValue, { color: theme.colors.text.primary }]}>
                {formatDuration(taskType.duration)}
              </Text>
              <Text style={[styles.listItemSubvalue, { color: theme.colors.text.secondary }]}>
                {taskType.sessions} sessions
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  if (sessions.length === 0) {
    return (
      <Card
        variant="elevated"
        style={[
          styles.container,
          { 
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          },
          style
        ]}
      >
        <View style={styles.emptyState}>
          <MaterialIcons 
            name="analytics" 
            size={48} 
            color={theme.colors.text.secondary} 
            style={styles.emptyIcon}
          />
          <Text style={[styles.emptyTitle, { color: theme.colors.text.primary }]}>
            No Session Data
          </Text>
          <Text style={[styles.emptySubtitle, { color: theme.colors.text.secondary }]}>
            Complete some study sessions to see your statistics here.
          </Text>
        </View>
      </Card>
    );
  }

  return (
    <Card
      variant="elevated"
      style={[
        styles.container,
        { 
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.border,
        },
        style
      ]}
    >
      {/* Header */}
      <View style={styles.header}>
        <MaterialIcons 
          name="analytics" 
          size={24} 
          color={theme.colors.primary} 
          style={styles.headerIcon}
        />
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          {title}
        </Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Basic Stats Grid */}
        <View style={styles.statsGrid}>
          {renderStatItem('timer', 'Total Sessions', stats.totalSessions.toString())}
          {renderStatItem('schedule', 'Total Time', formatDuration(stats.totalDuration))}
          {renderStatItem('avg-pace', 'Average Duration', formatDuration(stats.averageDuration))}
          {renderStatItem('check-circle', 'Completion Rate', formatPercentage(stats.completionRate), '#10B981')}
        </View>

        {showDetailedStats && (
          <>
            {/* Additional Stats */}
            <View style={styles.statsGrid}>
              {stats.averageProductivityRating > 0 && renderStatItem(
                'star', 
                'Avg. Productivity', 
                `${stats.averageProductivityRating.toFixed(1)}/5`,
                '#FFD700'
              )}
              {renderStatItem('play-circle', 'Pomodoro Sessions', stats.totalPomodoroSessions.toString())}
              {renderStatItem('timer', 'Stopwatch Sessions', stats.totalStopwatchSessions.toString())}
              {stats.longestSession > 0 && renderStatItem(
                'trending-up', 
                'Longest Session', 
                formatDuration(stats.longestSession)
              )}
            </View>

            {/* Top Subjects */}
            {renderTopSubjects()}

            {/* Top Task Types */}
            {renderTopTaskTypes()}
          </>
        )}
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 24,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    minWidth: '45%',
  },
  statIcon: {
    marginRight: 8,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  listItemRight: {
    alignItems: 'flex-end',
  },
  listItemTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  listItemValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  listItemSubvalue: {
    fontSize: 12,
    marginTop: 2,
  },
  subjectColorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});
