{"designSystem": {"name": "Productivity Timer App Design System", "theme": "dark", "description": "A comprehensive design system for productivity/study timer applications with focus on distraction blocking and Pomodoro technique", "colors": {"primary": {"background": "#000000", "surface": "#1C1C1E", "surfaceSecondary": "#2C2C2E", "surfaceElevated": "#3A3A3C", "surfaceActive": "#48484A"}, "text": {"primary": "#FFFFFF", "secondary": "#8E8E93", "tertiary": "#6D6D70", "muted": "#48484A"}, "accent": {"primary": "#007AFF", "success": "#30D158", "warning": "#FF9500", "error": "#FF3B30", "focus": "#5856D6", "break": "#FF9500"}, "gradient": {"timer": ["#1D1D1F", "#2C2C2E"], "focus": ["#5856D6", "#007AFF"], "break": ["#FF9500", "#FF6B35"], "active": ["#30D158", "#32D74B"]}, "status": {"active": "#30D158", "ready": "#007AFF", "disabled": "#8E8E93", "blocked": "#FF3B30"}}, "typography": {"fontFamily": "SF Pro Display, -apple-system, system-ui", "sizes": {"timerDisplay": {"fontSize": "48px", "fontWeight": "700", "lineHeight": "1.1", "letterSpacing": "-0.02em", "fontFeatureSettings": "'tnum'"}, "hero": {"fontSize": "34px", "fontWeight": "700", "lineHeight": "1.2", "letterSpacing": "-0.02em"}, "title": {"fontSize": "28px", "fontWeight": "600", "lineHeight": "1.3", "letterSpacing": "-0.01em"}, "headline": {"fontSize": "22px", "fontWeight": "600", "lineHeight": "1.4"}, "subheadline": {"fontSize": "20px", "fontWeight": "500", "lineHeight": "1.4"}, "body": {"fontSize": "17px", "fontWeight": "400", "lineHeight": "1.5"}, "callout": {"fontSize": "16px", "fontWeight": "400", "lineHeight": "1.4"}, "caption": {"fontSize": "13px", "fontWeight": "500", "lineHeight": "1.4", "textTransform": "uppercase", "letterSpacing": "0.08em"}, "small": {"fontSize": "11px", "fontWeight": "400", "lineHeight": "1.3"}}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "xxl": "48px", "xxxl": "64px"}, "borderRadius": {"xs": "4px", "sm": "8px", "md": "12px", "lg": "16px", "xl": "20px", "xxl": "24px", "full": "50%"}, "shadows": {"card": "0 2px 8px rgba(0, 0, 0, 0.3)", "elevated": "0 4px 16px rgba(0, 0, 0, 0.4)", "timer": "0 8px 32px rgba(0, 0, 0, 0.5)"}, "components": {"header": {"structure": "Logo on left, action buttons on right", "layout": "Flex row with space-between alignment", "padding": "md lg", "backgroundColor": "transparent", "logo": {"height": "32px", "color": "text.primary"}, "actions": {"display": "flex", "gap": "md", "alignItems": "center"}, "actionButton": {"padding": "sm md", "borderRadius": "md", "backgroundColor": "surfaceElevated", "color": "text.secondary", "fontSize": "callout", "fontWeight": "500"}}, "modeSelector": {"structure": "Horizontal toggle with two mode buttons", "layout": "Flex row", "backgroundColor": "surfaceSecondary", "borderRadius": "lg", "padding": "xs", "marginBottom": "lg", "button": {"flex": "1", "padding": "md", "borderRadius": "md", "display": "flex", "alignItems": "center", "justifyContent": "center", "gap": "sm", "fontSize": "callout", "fontWeight": "500", "transition": "all 0.2s ease", "states": {"inactive": {"backgroundColor": "transparent", "color": "text.secondary"}, "active": {"backgroundColor": "accent.primary", "color": "text.primary"}}}, "icon": {"size": "20px"}}, "subjectPicker": {"structure": "Horizontal scrollable list of subject chips", "layout": "ScrollView horizontal", "marginBottom": "lg", "gap": "md", "chip": {"minWidth": "48px", "height": "48px", "borderRadius": "full", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "headline", "fontWeight": "600", "states": {"inactive": {"backgroundColor": "surfaceSecondary", "color": "text.secondary"}, "active": {"backgroundColor": "accent.primary", "color": "text.primary"}}}, "addButton": {"width": "48px", "height": "48px", "borderRadius": "full", "backgroundColor": "surfaceSecondary", "border": "2px dashed", "borderColor": "text.tertiary", "color": "text.tertiary"}}, "focusStats": {"structure": "Three-column grid of stat cards", "layout": "Grid with 3 equal columns", "gap": "md", "marginBottom": "lg", "conditionalDisplay": "Only when timer is running", "statCard": {"backgroundColor": "surfaceElevated", "borderRadius": "lg", "padding": "md", "textAlign": "center", "icon": {"size": "20px", "color": "accent.primary", "marginBottom": "xs"}, "label": {"fontSize": "caption", "color": "text.secondary", "marginBottom": "xs"}, "value": {"fontSize": "headline", "fontWeight": "700", "color": "text.primary"}}}, "mainTimerCard": {"structure": "Large central card with gradient background", "backgroundColor": "gradient.timer", "borderRadius": "xxl", "padding": "xl", "marginBottom": "lg", "boxShadow": "timer", "timerHeader": {"marginBottom": "lg", "phaseIndicator": {"display": "flex", "alignItems": "center", "gap": "sm", "marginBottom": "sm", "icon": {"fontSize": "headline"}, "label": {"fontSize": "callout", "fontWeight": "500", "color": "text.secondary"}}, "cycleText": {"fontSize": "caption", "color": "text.tertiary"}, "focusIndicator": {"display": "flex", "alignItems": "center", "gap": "xs", "padding": "xs sm", "backgroundColor": "status.active", "borderRadius": "sm", "fontSize": "small", "fontWeight": "500", "color": "text.primary"}}, "timerDisplay": {"textAlign": "center", "marginBottom": "lg", "time": {"fontSize": "timerDisplay", "fontWeight": "700", "color": "text.primary", "marginBottom": "sm"}, "progressContainer": {"width": "100%", "marginBottom": "md", "progressTrack": {"height": "4px", "backgroundColor": "surfaceSecondary", "borderRadius": "full", "overflow": "hidden"}, "progressFill": {"height": "100%", "backgroundColor": "accent.primary", "borderRadius": "full", "transition": "width 0.3s ease"}, "percentage": {"fontSize": "caption", "color": "text.secondary", "textAlign": "center", "marginTop": "sm"}}}}, "timerControls": {"structure": "Three control buttons in horizontal layout", "layout": "Flex row with center alignment", "gap": "lg", "marginBottom": "xl", "justifyContent": "center", "controlButton": {"width": "56px", "height": "56px", "borderRadius": "full", "display": "flex", "alignItems": "center", "justifyContent": "center", "backgroundColor": "surfaceElevated", "transition": "all 0.2s ease", "icon": {"size": "24px", "color": "text.primary"}}, "playButton": {"width": "72px", "height": "72px", "backgroundColor": "accent.primary", "boxShadow": "elevated"}}, "sectionCard": {"structure": "Standard card layout for sections", "backgroundColor": "surfaceElevated", "borderRadius": "lg", "padding": "lg", "marginBottom": "lg", "header": {"display": "flex", "alignItems": "center", "justifyContent": "space-between", "marginBottom": "md", "title": {"fontSize": "headline", "fontWeight": "600", "color": "text.primary"}, "action": {"fontSize": "callout", "color": "text.secondary"}}}, "distractionBlocking": {"extends": "sectionCard", "blockingCard": {"padding": "md", "borderRadius": "md", "marginBottom": "md", "textAlign": "center", "states": {"active": {"backgroundColor": "status.active", "color": "text.primary"}, "ready": {"backgroundColor": "status.ready", "color": "text.primary"}, "disabled": {"backgroundColor": "surfaceSecondary", "color": "text.secondary"}}, "status": {"fontSize": "caption", "fontWeight": "600", "marginBottom": "xs"}, "description": {"fontSize": "small", "opacity": "0.8"}}, "toggleButton": {"width": "100%", "padding": "md", "borderRadius": "md", "backgroundColor": "accent.primary", "color": "text.primary", "fontSize": "callout", "fontWeight": "500", "marginBottom": "md"}, "recentlyBlocked": {"marginTop": "md", "title": {"fontSize": "caption", "color": "text.secondary", "marginBottom": "sm"}, "appList": {"display": "flex", "flexWrap": "wrap", "gap": "xs"}, "appChip": {"padding": "xs sm", "backgroundColor": "surfaceSecondary", "borderRadius": "sm", "fontSize": "small", "color": "text.secondary"}}}, "focusSounds": {"extends": "sectionCard", "conditionalDisplay": "Only when timer is running", "soundGrid": {"display": "grid", "gridTemplateColumns": "repeat(2, 1fr)", "gap": "md"}, "soundCard": {"padding": "md", "borderRadius": "md", "textAlign": "center", "transition": "all 0.2s ease", "states": {"inactive": {"backgroundColor": "surfaceSecondary", "color": "text.secondary"}, "active": {"backgroundColor": "accent.primary", "color": "text.primary"}}, "icon": {"fontSize": "headline", "marginBottom": "xs"}, "label": {"fontSize": "callout", "fontWeight": "500"}, "speakerIcon": {"position": "absolute", "top": "xs", "right": "xs", "size": "12px"}}}, "todaysProgress": {"extends": "sectionCard", "progressGrid": {"display": "grid", "gridTemplateColumns": "1fr 1fr", "gap": "md"}, "metric": {"textAlign": "center", "label": {"fontSize": "caption", "color": "text.secondary", "marginBottom": "xs"}, "value": {"fontSize": "headline", "fontWeight": "600", "color": "text.primary"}}}, "quickTips": {"extends": "sectionCard", "tipContent": {"display": "flex", "alignItems": "flex-start", "gap": "md", "icon": {"size": "20px", "color": "accent.primary", "marginTop": "xs"}, "text": {"fontSize": "callout", "color": "text.secondary", "lineHeight": "1.5"}}}, "blockingNotifications": {"position": "fixed", "bottom": "xl", "left": "md", "right": "md", "zIndex": "1000", "notification": {"backgroundColor": "surfaceElevated", "borderRadius": "lg", "padding": "md", "marginBottom": "sm", "display": "flex", "alignItems": "center", "gap": "md", "boxShadow": "elevated", "animation": "slideUp 0.3s ease-out", "icon": {"size": "20px", "color": "accent.primary"}, "text": {"fontSize": "callout", "color": "text.primary", "flex": "1"}, "dismissButton": {"size": "16px", "color": "text.tertiary"}}}}, "layoutPatterns": {"screenContainer": {"flex": "1", "backgroundColor": "primary.background"}, "scrollContent": {"padding": "md", "paddingBottom": "xxxl"}, "sectionSpacing": {"marginBottom": "lg"}, "conditionalSection": {"display": "conditional", "animation": "fadeIn 0.3s ease-out"}}, "interactions": {"buttonPress": {"transform": "scale(0.95)", "transition": "all 0.1s ease"}, "cardHover": {"transform": "translateY(-2px)", "transition": "all 0.2s ease"}, "slideUp": {"from": {"transform": "translateY(100%)", "opacity": "0"}, "to": {"transform": "translateY(0)", "opacity": "1"}}, "fadeIn": {"from": {"opacity": "0"}, "to": {"opacity": "1"}}, "progressFill": {"transition": "width 0.5s ease-out"}}, "stateManagement": {"timerStates": {"idle": "Timer not running", "running": "Timer active - shows focus stats, sounds, notifications", "paused": "Timer paused - maintains current progress", "break": "Break period active in Pomodoro mode"}, "blockingStates": {"disabled": "Blocking not enabled in settings", "ready": "Blocking enabled but not active", "active": "Blocking currently active", "manual": "Manual blocking mode active"}, "conditionalComponents": ["focusStats: visible when timer running", "focusSounds: visible when timer running", "distractionBlockingDemo: visible when timer running and blocking enabled", "recentlyBlocked: visible when apps have been blocked recently"]}, "designPrinciples": {"focusFirst": "Large, prominent timer display as central element", "progressiveDisclosure": "Show relevant controls and info based on timer state", "accessibleInteraction": "Large touch targets for primary actions", "visualHierarchy": "Clear typography and spacing hierarchy", "contextualFeedback": "Status indicators and notifications for user awareness", "minimalistDesign": "Clean, distraction-free interface supporting focus"}, "implementationNotes": {"responsiveDesign": "Optimized for mobile-first with touch interactions", "performanceOptimization": "Efficient re-renders for timer updates", "accessibilityCompliance": "WCAG guidelines for color contrast and navigation", "stateConsistency": "Reliable state management across timer modes", "notificationSystem": "Non-intrusive notification display system"}}}