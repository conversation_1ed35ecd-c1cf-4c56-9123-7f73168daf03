import { useState, useEffect } from 'react';
import { Goal } from '@/types/app';
import { Platform } from 'react-native';
import { storageService } from '@/services/storageService';

const STORAGE_KEY = 'isotope_goals';

export function useGoals() {
  const [goals, setGoals] = useState<Goal[]>([]);

  useEffect(() => {
    loadGoals();
  }, []);

  const loadGoals = async () => {
    try {
      const stored = await storageService.getObject<any[]>(STORAGE_KEY);
      if (stored) {
        setGoals(stored.map((g: any) => ({
          ...g,
          targetDate: new Date(g.targetDate),
          createdAt: new Date(g.createdAt),
        })));
      }
    } catch (error) {
      console.error('Error loading goals:', error);
    }
  };

  const saveGoals = async (newGoals: Goal[]) => {
    try {
      await storageService.setObject(STORAGE_KEY, newGoals);
      setGoals(newGoals);
    } catch (error) {
      console.error('Error saving goals:', error);
    }
  };

  const addGoal = (goalData: Omit<Goal, 'id' | 'createdAt'>) => {
    const newGoal: Goal = {
      id: Date.now().toString(),
      ...goalData,
      createdAt: new Date(),
    };
    const newGoals = [...goals, newGoal];
    saveGoals(newGoals);
  };

  const updateGoal = (id: string, updates: Partial<Goal>) => {
    const newGoals = goals.map(goal =>
      goal.id === id ? { ...goal, ...updates } : goal
    );
    saveGoals(newGoals);
  };

  const deleteGoal = (id: string) => {
    const newGoals = goals.filter(goal => goal.id !== id);
    saveGoals(newGoals);
  };

  const toggleGoalCompletion = (id: string) => {
    updateGoal(id, { completed: !goals.find(g => g.id === id)?.completed });
  };

  const getGoalsByPriority = (priority: 'low' | 'medium' | 'high') => {
    return goals.filter(goal => goal.priority === priority && !goal.completed);
  };

  const getCompletedGoals = () => {
    return goals.filter(goal => goal.completed);
  };

  const getUpcomingGoals = () => {
    const now = new Date();
    return goals
      .filter(goal => !goal.completed && goal.targetDate > now)
      .sort((a, b) => a.targetDate.getTime() - b.targetDate.getTime());
  };

  const getOverdueGoals = () => {
    const now = new Date();
    return goals.filter(goal => !goal.completed && goal.targetDate < now);
  };

  return {
    goals,
    addGoal,
    updateGoal,
    deleteGoal,
    toggleGoalCompletion,
    getGoalsByPriority,
    getCompletedGoals,
    getUpcomingGoals,
    getOverdueGoals,
  };
}