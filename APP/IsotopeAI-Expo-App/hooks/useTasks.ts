import { useState, useEffect, useCallback } from 'react';
import { Task, TaskCategory, TaskFilters, TaskStats } from '@/types/app';
import { taskService, CreateTaskData, UpdateTaskData } from '@/services/taskService';
import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

export function useTasks() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [categories, setCategories] = useState<TaskCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [realtimeChannel, setRealtimeChannel] = useState<RealtimeChannel | null>(null);

  // Get current user
  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  };

  // Load tasks and categories
  const loadData = useCallback(async (filters?: TaskFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const user = await getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Initialize categories if this is the first time
      await taskService.initializeUserCategories(user.id);

      // Load tasks and categories
      const [tasksData, categoriesData] = await Promise.all([
        taskService.getTasks(user.id, filters),
        taskService.getCategories(user.id),
      ]);

      // Convert date strings to Date objects
      const processedTasks = tasksData.map(task => ({
        ...task,
        due_date: task.due_date ? new Date(task.due_date) : undefined,
        start_date: task.start_date ? new Date(task.start_date) : undefined,
        completion_date: task.completion_date ? new Date(task.completion_date) : undefined,
        reminder_time: task.reminder_time ? new Date(task.reminder_time) : undefined,
        created_at: new Date(task.created_at),
        updated_at: new Date(task.updated_at),
      }));

      const processedCategories = categoriesData.map(category => ({
        ...category,
        created_at: new Date(category.created_at),
        updated_at: new Date(category.updated_at),
      }));

      setTasks(processedTasks);
      setCategories(processedCategories);
    } catch (err) {
      console.error('Error loading tasks:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tasks');
    } finally {
      setLoading(false);
    }
  }, []);

  // Setup real-time subscription
  const setupRealtimeSubscription = useCallback(async () => {
    const user = await getCurrentUser();
    if (!user) return;

    const channel = taskService.subscribeToTasks(user.id, (payload) => {
      console.log('Real-time update:', payload);
      // Reload data when changes occur
      loadData();
    });

    setRealtimeChannel(channel);
  }, [loadData]);

  // Initialize data and real-time subscription
  useEffect(() => {
    loadData();
    setupRealtimeSubscription();

    return () => {
      if (realtimeChannel) {
        taskService.unsubscribeFromTasks();
      }
    };
  }, []);

  // Task operations
  const createTask = async (taskData: CreateTaskData): Promise<Task | null> => {
    try {
      const user = await getCurrentUser();
      if (!user) throw new Error('User not authenticated');

      const newTask = await taskService.createTask(user.id, taskData);
      if (newTask) {
        // Convert date strings to Date objects
        const processedTask = {
          ...newTask,
          due_date: newTask.due_date ? new Date(newTask.due_date) : undefined,
          start_date: newTask.start_date ? new Date(newTask.start_date) : undefined,
          completion_date: newTask.completion_date ? new Date(newTask.completion_date) : undefined,
          reminder_time: newTask.reminder_time ? new Date(newTask.reminder_time) : undefined,
          created_at: new Date(newTask.created_at),
          updated_at: new Date(newTask.updated_at),
        };
        
        setTasks(prev => [processedTask, ...prev]);
        return processedTask;
      }
      return null;
    } catch (err) {
      console.error('Error creating task:', err);
      setError(err instanceof Error ? err.message : 'Failed to create task');
      return null;
    }
  };

  const updateTask = async (taskId: string, updates: UpdateTaskData): Promise<Task | null> => {
    try {
      const updatedTask = await taskService.updateTask(taskId, updates);
      if (updatedTask) {
        // Convert date strings to Date objects
        const processedTask = {
          ...updatedTask,
          due_date: updatedTask.due_date ? new Date(updatedTask.due_date) : undefined,
          start_date: updatedTask.start_date ? new Date(updatedTask.start_date) : undefined,
          completion_date: updatedTask.completion_date ? new Date(updatedTask.completion_date) : undefined,
          reminder_time: updatedTask.reminder_time ? new Date(updatedTask.reminder_time) : undefined,
          created_at: new Date(updatedTask.created_at),
          updated_at: new Date(updatedTask.updated_at),
        };

        setTasks(prev => prev.map(task => 
          task.id === taskId ? processedTask : task
        ));
        return processedTask;
      }
      return null;
    } catch (err) {
      console.error('Error updating task:', err);
      setError(err instanceof Error ? err.message : 'Failed to update task');
      return null;
    }
  };

  const deleteTask = async (taskId: string): Promise<boolean> => {
    try {
      const success = await taskService.deleteTask(taskId);
      if (success) {
        setTasks(prev => prev.filter(task => task.id !== taskId));
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error deleting task:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete task');
      return false;
    }
  };

  const completeTask = async (taskId: string): Promise<Task | null> => {
    return updateTask(taskId, { 
      status: 'completed', 
      progress_percentage: 100,
      completion_date: new Date() 
    });
  };

  const updateTaskProgress = async (taskId: string, progress: number): Promise<Task | null> => {
    const updates: UpdateTaskData = { progress_percentage: progress };
    
    if (progress >= 100) {
      updates.status = 'completed';
      updates.completion_date = new Date();
    }
    
    return updateTask(taskId, updates);
  };

  // Category operations
  const createCategory = async (categoryData: Omit<TaskCategory, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<TaskCategory | null> => {
    try {
      const user = await getCurrentUser();
      if (!user) throw new Error('User not authenticated');

      const newCategory = await taskService.createCategory(user.id, categoryData);
      if (newCategory) {
        const processedCategory = {
          ...newCategory,
          created_at: new Date(newCategory.created_at),
          updated_at: new Date(newCategory.updated_at),
        };
        
        setCategories(prev => [...prev, processedCategory]);
        return processedCategory;
      }
      return null;
    } catch (err) {
      console.error('Error creating category:', err);
      setError(err instanceof Error ? err.message : 'Failed to create category');
      return null;
    }
  };

  // Utility functions
  const getTasksByStatus = (status: Task['status']) => {
    return tasks.filter(task => task.status === status);
  };

  const getTasksByCategory = (categoryName: string) => {
    return tasks.filter(task => task.category === categoryName);
  };

  const getOverdueTasks = () => {
    const now = new Date();
    return tasks.filter(task => 
      task.due_date && 
      task.due_date < now && 
      task.status !== 'completed'
    );
  };

  const getUpcomingTasks = (days: number = 7) => {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + days);

    return tasks.filter(task => 
      task.due_date && 
      task.due_date >= now && 
      task.due_date <= futureDate &&
      task.status !== 'completed'
    );
  };

  const getTaskStats = async (): Promise<TaskStats> => {
    try {
      const user = await getCurrentUser();
      if (!user) throw new Error('User not authenticated');

      return await taskService.getTaskStats(user.id);
    } catch (err) {
      console.error('Error getting task stats:', err);
      return {
        total_tasks: 0,
        completed_tasks: 0,
        in_progress_tasks: 0,
        overdue_tasks: 0,
        completion_rate: 0,
        total_study_time: 0,
        average_completion_time: 0,
        tasks_by_category: {},
        tasks_by_priority: {},
      };
    }
  };

  const filterTasks = (filters: TaskFilters) => {
    loadData(filters);
  };

  return {
    // Data
    tasks,
    categories,
    loading,
    error,

    // Task operations
    createTask,
    updateTask,
    deleteTask,
    completeTask,
    updateTaskProgress,

    // Category operations
    createCategory,

    // Utility functions
    getTasksByStatus,
    getTasksByCategory,
    getOverdueTasks,
    getUpcomingTasks,
    getTaskStats,
    filterTasks,
    refreshTasks: loadData,
  };
}
