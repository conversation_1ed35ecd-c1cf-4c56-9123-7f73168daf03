import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { TimerSession, SessionSummary, TimerMode, TaskType } from '@/types/app';
import { useSessionStore } from '@/stores/sessionStore';
import { useAuth } from '@/hooks/useAuth';

interface UseSessionManagementOptions {
  autoSync?: boolean;
  enableRealtime?: boolean;
}

interface SessionCreationData {
  subject?: string;
  subjectColor?: string;
  mode?: TimerMode;
  phase?: 'work' | 'shortBreak' | 'longBreak';
  taskName?: string;
  taskType?: TaskType;
}

export function useSessionManagement(options: UseSessionManagementOptions = {}) {
  const { autoSync = true, enableRealtime = true } = options;
  const { user } = useAuth();
  const {
    sessions,
    currentSession,
    isLoading,
    error,
    fetchSessions,
    createSession,
    updateSession,
    completeSession,
    startSession,
    endSession,
    subscribeToRealtime,
    unsubscribeFromRealtime,
    clearError,
  } = useSessionStore();

  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize session management
  useEffect(() => {
    if (!user?.id || isInitialized) return;

    const initialize = async () => {
      try {
        if (autoSync) {
          await fetchSessions(user.id);
        }
        
        if (enableRealtime) {
          subscribeToRealtime(user.id);
        }
        
        setIsInitialized(true);
      } catch (error: any) {
        console.error('Failed to initialize session management:', error);
        Alert.alert('Error', 'Failed to initialize session management');
      }
    };

    initialize();

    // Cleanup on unmount
    return () => {
      if (enableRealtime) {
        unsubscribeFromRealtime();
      }
    };
  }, [user?.id, autoSync, enableRealtime, isInitialized]);

  // Create and start a new session
  const createAndStartSession = useCallback(async (data: SessionCreationData): Promise<TimerSession | null> => {
    if (!user?.id) {
      Alert.alert('Error', 'User not authenticated');
      return null;
    }

    try {
      const session = await startSession(user.id, data);
      return session;
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to start session');
      return null;
    }
  }, [user?.id, startSession]);

  // Complete current session with feedback
  const completeCurrentSession = useCallback(async (summary: SessionSummary): Promise<TimerSession | null> => {
    if (!currentSession) {
      Alert.alert('Error', 'No active session to complete');
      return null;
    }

    try {
      const completedSession = await completeSession(currentSession.id, summary);
      return completedSession;
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to complete session');
      return null;
    }
  }, [currentSession, completeSession]);

  // End current session without completion (pause/cancel)
  const endCurrentSession = useCallback(async (summary?: SessionSummary): Promise<TimerSession | null> => {
    if (!currentSession) {
      Alert.alert('Error', 'No active session to end');
      return null;
    }

    try {
      const endedSession = await endSession(currentSession.id, summary);
      return endedSession;
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to end session');
      return null;
    }
  }, [currentSession, endSession]);

  // Update current session
  const updateCurrentSession = useCallback(async (updates: Partial<TimerSession>): Promise<TimerSession | null> => {
    if (!currentSession) {
      Alert.alert('Error', 'No active session to update');
      return null;
    }

    try {
      const updatedSession = await updateSession(currentSession.id, updates);
      return updatedSession;
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update session');
      return null;
    }
  }, [currentSession, updateSession]);

  // Refresh sessions data
  const refreshSessions = useCallback(async (): Promise<void> => {
    if (!user?.id) return;

    try {
      await fetchSessions(user.id, true);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to refresh sessions');
    }
  }, [user?.id, fetchSessions]);

  // Get sessions by date range
  const getSessionsByDateRange = useCallback((startDate: Date, endDate: Date): TimerSession[] => {
    return sessions.filter(session => {
      const sessionDate = new Date(session.date);
      return sessionDate >= startDate && sessionDate <= endDate;
    });
  }, [sessions]);

  // Get sessions by subject
  const getSessionsBySubject = useCallback((subject: string): TimerSession[] => {
    return sessions.filter(session => session.subject === subject);
  }, [sessions]);

  // Get today's sessions
  const getTodaySessions = useCallback((): TimerSession[] => {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    return sessions.filter(session => session.date === todayStr);
  }, [sessions]);

  // Get this week's sessions
  const getThisWeekSessions = useCallback((): TimerSession[] => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return getSessionsByDateRange(startOfWeek, endOfWeek);
  }, [getSessionsByDateRange]);

  // Calculate session statistics
  const getSessionStats = useCallback(() => {
    const totalSessions = sessions.length;
    const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);
    const completedSessions = sessions.filter(session => session.completed).length;
    const averageDuration = totalSessions > 0 ? totalDuration / totalSessions : 0;
    const completionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;

    // Calculate average productivity rating
    const ratingsSum = sessions
      .filter(session => session.productivityRating)
      .reduce((sum, session) => sum + (session.productivityRating || 0), 0);
    const ratingsCount = sessions.filter(session => session.productivityRating).length;
    const averageProductivityRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

    return {
      totalSessions,
      totalDuration,
      completedSessions,
      averageDuration,
      completionRate,
      averageProductivityRating,
    };
  }, [sessions]);

  // Check if there's an active session
  const hasActiveSession = useCallback((): boolean => {
    return currentSession !== null && !currentSession.completed;
  }, [currentSession]);

  // Get current session duration (live)
  const getCurrentSessionDuration = useCallback((): number => {
    if (!currentSession || !currentSession.startTime) return 0;
    
    const now = new Date();
    const startTime = new Date(currentSession.startTime);
    return Math.floor((now.getTime() - startTime.getTime()) / 1000);
  }, [currentSession]);

  // Clear any errors
  const dismissError = useCallback(() => {
    clearError();
  }, [clearError]);

  return {
    // State
    sessions,
    currentSession,
    isLoading,
    error,
    isInitialized,

    // Session management
    createAndStartSession,
    completeCurrentSession,
    endCurrentSession,
    updateCurrentSession,
    refreshSessions,

    // Data queries
    getSessionsByDateRange,
    getSessionsBySubject,
    getTodaySessions,
    getThisWeekSessions,
    getSessionStats,

    // Utilities
    hasActiveSession,
    getCurrentSessionDuration,
    dismissError,
  };
}

export type SessionManagement = ReturnType<typeof useSessionManagement>;
