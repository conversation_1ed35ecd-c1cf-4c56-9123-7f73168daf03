import { useState, useEffect, useRef } from 'react';
import { TimerSession, Subject, PomodoroSettings } from '@/types/app';
import { Platform } from 'react-native';
import { storageService } from '@/services/storageService';
import { supabaseService } from '@/services/supabaseService';
import { useAuth } from '@/contexts/AuthContext';
import { useSubjects } from './useSubjects';

const SESSIONS_STORAGE_KEY = 'isotope_timer_sessions';
const POMODORO_SETTINGS_KEY = 'isotope_pomodoro_settings';

const DEFAULT_POMODORO_SETTINGS: PomodoroSettings = {
  workDuration: 25,
  shortBreakDuration: 5,
  longBreakDuration: 15,
  sessionsUntilLongBreak: 4,
};

export function useTimer() {
  const { user } = useAuth();
  const { subjects } = useSubjects();
  const [isRunning, setIsRunning] = useState(false);
  const [time, setTime] = useState(0); // in seconds
  const [mode, setMode] = useState<'stopwatch' | 'pomodoro'>('stopwatch');
  const [currentSubject, setCurrentSubject] = useState<Subject | null>(null);
  const [sessions, setSessions] = useState<TimerSession[]>([]);
  const [pomodoroSettings, setPomodoroSettings] = useState<PomodoroSettings>(DEFAULT_POMODORO_SETTINGS);
  const [pomodoroPhase, setPomodoroPhase] = useState<'work' | 'break'>('work');
  const [pomodoroSession, setPomodoroSession] = useState(1);
  const [loading, setLoading] = useState(false);

  const intervalRef = useRef<any>(null);
  const currentSessionRef = useRef<TimerSession | null>(null);

  useEffect(() => {
    if (user) {
      loadSessions();
      loadPomodoroSettings();
    }
  }, [user]);

  useEffect(() => {
    if (!currentSubject && subjects.length > 0) {
      setCurrentSubject(subjects[0]);
    }
  }, [subjects, currentSubject]);

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setTime(prevTime => {
          const newTime = mode === 'stopwatch' ? prevTime + 1 : prevTime - 1;
          
          // Pomodoro timer reached zero
          if (mode === 'pomodoro' && newTime <= 0) {
            handlePomodoroComplete();
            return 0;
          }
          
          return newTime;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, mode]);

  const loadSessions = async () => {
    if (!user) return;

    try {
      setLoading(true);
      // Try to load from Supabase first
      const supabaseSessions = await supabaseService.getStudySessions(user.id);
      if (supabaseSessions.length > 0) {
        setSessions(supabaseSessions);
      } else {
        // Fallback to local storage for migration
        const stored = await storageService.getObject<any[]>(SESSIONS_STORAGE_KEY);
        if (stored) {
          const localSessions = stored.map((s: any) => ({
            ...s,
            startTime: new Date(s.startTime),
            endTime: s.endTime ? new Date(s.endTime) : undefined,
          }));
          setSessions(localSessions);

          // Migrate local sessions to Supabase
          for (const session of localSessions) {
            await supabaseService.createStudySession(user.id, session);
          }
        }
      }
    } catch (error) {
      console.error('Error loading sessions:', error);
      // Fallback to local storage on error
      try {
        const stored = await storageService.getObject<any[]>(SESSIONS_STORAGE_KEY);
        if (stored) {
          setSessions(stored.map((s: any) => ({
            ...s,
            startTime: new Date(s.startTime),
            endTime: s.endTime ? new Date(s.endTime) : undefined,
          })));
        }
      } catch (localError) {
        console.error('Error loading local sessions:', localError);
      }
    } finally {
      setLoading(false);
    }
  };

  const loadPomodoroSettings = async () => {
    try {
      const stored = await storageService.getObject<PomodoroSettings>(POMODORO_SETTINGS_KEY);
      if (stored) {
        setPomodoroSettings(stored);
      }
    } catch (error) {
      console.error('Error loading pomodoro settings:', error);
    }
  };

  const saveSessions = async (newSessions: TimerSession[]) => {
    try {
      // Save to local storage for offline support
      await storageService.setObject(SESSIONS_STORAGE_KEY, newSessions);
      setSessions(newSessions);
    } catch (error) {
      console.error('Error saving sessions:', error);
    }
  };

  const saveSessionToSupabase = async (session: TimerSession) => {
    if (!user) return;

    try {
      if (session.id && session.id.length > 10) {
        // Update existing session
        await supabaseService.updateStudySession(session.id, session);
      } else {
        // Create new session
        const newSession = await supabaseService.createStudySession(user.id, session);
        if (newSession) {
          // Update local sessions with the new ID
          setSessions(prev => prev.map(s =>
            s.id === session.id ? newSession : s
          ));
        }
      }
    } catch (error) {
      console.error('Error saving session to Supabase:', error);
    }
  };

  const savePomodoroSettings = async (settings: PomodoroSettings) => {
    try {
      await storageService.setObject(POMODORO_SETTINGS_KEY, settings);
      setPomodoroSettings(settings);
    } catch (error) {
      console.error('Error saving pomodoro settings:', error);
    }
  };

  const startTimer = () => {
    if (!isRunning) {
      // Create new session
      const newSession: TimerSession = {
        id: Date.now().toString(),
        startTime: new Date(),
        endTime: null,
        duration: 0,
        subject: currentSubject?.name,
        subjectId: currentSubject?.id,
        subjectColor: currentSubject?.color,
        mode,
        phase: mode === 'pomodoro' ? pomodoroPhase : undefined,
        completed: false,
        date: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
      };
      currentSessionRef.current = newSession;
    }
    setIsRunning(true);
  };

  const pauseTimer = () => {
    setIsRunning(false);
  };

  const stopTimer = () => {
    setIsRunning(false);

    // Save current session if it exists
    if (currentSessionRef.current) {
      const session = currentSessionRef.current;
      session.endTime = new Date();
      session.duration = mode === 'stopwatch' ? time :
        (pomodoroPhase === 'work' ? pomodoroSettings.workDuration * 60 - time :
         pomodoroSettings.shortBreakDuration * 60 - time);
      session.completed = true;

      const newSessions = [...sessions, session];
      saveSessions(newSessions);

      // Save to Supabase
      saveSessionToSupabase(session);

      currentSessionRef.current = null;
    }

    resetTimer();
  };

  const resetTimer = () => {
    setTime(mode === 'stopwatch' ? 0 : getPomodoroTime());
    setIsRunning(false);
    if (mode === 'pomodoro') {
      setPomodoroPhase('work');
      setPomodoroSession(1);
    }
  };

  const switchMode = (newMode: 'stopwatch' | 'pomodoro') => {
    if (isRunning) {
      stopTimer();
    }
    setMode(newMode);
    setTime(newMode === 'stopwatch' ? 0 : pomodoroSettings.workDuration * 60);
    if (newMode === 'pomodoro') {
      setPomodoroPhase('work');
      setPomodoroSession(1);
    }
  };

  const getPomodoroTime = () => {
    if (pomodoroPhase === 'work') {
      return pomodoroSettings.workDuration * 60;
    } else {
      const isLongBreak = pomodoroSession % pomodoroSettings.sessionsUntilLongBreak === 0;
      return isLongBreak ? pomodoroSettings.longBreakDuration * 60 : pomodoroSettings.shortBreakDuration * 60;
    }
  };

  const handlePomodoroComplete = () => {
    // Save completed session
    if (currentSessionRef.current) {
      const session = currentSessionRef.current;
      session.endTime = new Date();
      session.duration = pomodoroPhase === 'work' ? pomodoroSettings.workDuration * 60 :
        (pomodoroSession % pomodoroSettings.sessionsUntilLongBreak === 0 ?
         pomodoroSettings.longBreakDuration * 60 : pomodoroSettings.shortBreakDuration * 60);
      session.completed = true;

      const newSessions = [...sessions, session];
      saveSessions(newSessions);

      // Save to Supabase
      saveSessionToSupabase(session);
    }

    // Switch phase
    if (pomodoroPhase === 'work') {
      setPomodoroPhase('break');
      const isLongBreak = pomodoroSession % pomodoroSettings.sessionsUntilLongBreak === 0;
      setTime(isLongBreak ? pomodoroSettings.longBreakDuration * 60 : pomodoroSettings.shortBreakDuration * 60);
    } else {
      setPomodoroPhase('work');
      setPomodoroSession(prev => prev + 1);
      setTime(pomodoroSettings.workDuration * 60);
    }

    // Create new session for next phase
    const newSession: TimerSession = {
      id: Date.now().toString(),
      subjectId: currentSubject?.id,
      startTime: new Date(),
      endTime: null,
      duration: 0,
      subject: currentSubject?.name,
      subjectColor: currentSubject?.color,
      mode: 'pomodoro',
      phase: pomodoroPhase === 'work' ? 'break' : 'work',
      completed: false,
      date: new Date().toISOString().split('T')[0], // YYYY-MM-DD format
      isBreak: pomodoroPhase === 'work', // Will be break in next phase
    };
    currentSessionRef.current = newSession;
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTodaysSessions = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return sessions.filter(session => 
      session.startTime >= today && session.startTime < tomorrow
    );
  };

  const getTotalTimeToday = () => {
    return getTodaysSessions().reduce((total, session) => total + session.duration, 0);
  };

  return {
    isRunning,
    time,
    mode,
    currentSubject,
    sessions,
    pomodoroSettings,
    pomodoroPhase,
    pomodoroSession,
    loading,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    switchMode,
    setCurrentSubject,
    formatTime,
    getTodaysSessions,
    getTotalTimeToday,
    savePomodoroSettings,
  };
}
