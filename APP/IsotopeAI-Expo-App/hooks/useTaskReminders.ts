import { useEffect, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { notificationService } from '@/services/notificationService';
import { Task } from '@/types/app';

interface UseTaskRemindersOptions {
  tasks: Task[];
  onTaskCompleted?: (task: Task) => void;
  onStreakAchieved?: (streakCount: number, streakType: 'daily' | 'weekly') => void;
}

export const useTaskReminders = ({ 
  tasks, 
  onTaskCompleted, 
  onStreakAchieved 
}: UseTaskRemindersOptions) => {
  
  // Initialize notification service when hook is first used
  useEffect(() => {
    notificationService.initialize();
  }, []);

  // Schedule reminders for all tasks when tasks change
  const scheduleAllReminders = useCallback(async () => {
    console.log('📱 Scheduling reminders for', tasks.length, 'tasks');
    
    for (const task of tasks) {
      if (task.status === 'completed' || task.status === 'cancelled') {
        // Cancel reminders for completed/cancelled tasks
        await notificationService.cancelTaskReminders(task.id);
        continue;
      }

      if (task.reminder_enabled) {
        await notificationService.rescheduleTaskReminders(task);
      }
    }
  }, [tasks]);

  // Schedule reminders when tasks change
  useEffect(() => {
    scheduleAllReminders();
  }, [scheduleAllReminders]);

  // Handle app state changes to manage notifications
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App came to foreground, check for overdue tasks
        checkOverdueTasks();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [tasks]);

  const checkOverdueTasks = useCallback(async () => {
    const now = new Date();
    const overdueTasks = tasks.filter(task => 
      task.due_date && 
      new Date(task.due_date) < now && 
      task.status !== 'completed' && 
      task.status !== 'cancelled'
    );

    for (const task of overdueTasks) {
      await notificationService.scheduleTaskReminder({
        task,
        reminderType: 'overdue'
      });
    }
  }, [tasks]);

  const scheduleTaskReminder = useCallback(async (
    task: Task, 
    reminderType: 'due_soon' | 'overdue' | 'milestone_due' | 'custom' = 'custom',
    customMessage?: string
  ) => {
    return await notificationService.scheduleTaskReminder({
      task,
      reminderType,
      customMessage
    });
  }, []);

  const cancelTaskReminders = useCallback(async (taskId: string) => {
    await notificationService.cancelTaskReminders(taskId);
  }, []);

  const showTaskCompletionNotification = useCallback(async (task: Task) => {
    await notificationService.showTaskCompletionNotification(task);
    onTaskCompleted?.(task);
    
    // Check for streaks
    await checkAndNotifyStreaks();
  }, [onTaskCompleted, tasks]);

  const checkAndNotifyStreaks = useCallback(async () => {
    const completedTasks = tasks.filter(task => task.status === 'completed');
    
    // Calculate daily streak
    const dailyStreak = calculateDailyStreak(completedTasks);
    if (dailyStreak > 1 && dailyStreak % 3 === 0) { // Notify every 3 days
      await notificationService.showStreakAchievementNotification(dailyStreak, 'daily');
      onStreakAchieved?.(dailyStreak, 'daily');
    }

    // Calculate weekly streak
    const weeklyStreak = calculateWeeklyStreak(completedTasks);
    if (weeklyStreak > 1) {
      await notificationService.showStreakAchievementNotification(weeklyStreak, 'weekly');
      onStreakAchieved?.(weeklyStreak, 'weekly');
    }
  }, [tasks, onStreakAchieved]);

  const showProductivityMilestone = useCallback(async (milestone: string) => {
    await notificationService.showProductivityMilestoneNotification(milestone);
  }, []);

  // Helper functions for streak calculation
  const calculateDailyStreak = (completedTasks: Task[]): number => {
    const today = new Date();
    let streak = 0;
    let currentDate = new Date(today);

    while (true) {
      const dayStart = new Date(currentDate);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(currentDate);
      dayEnd.setHours(23, 59, 59, 999);

      const tasksCompletedToday = completedTasks.filter(task => {
        if (!task.completion_date) return false;
        const completionDate = new Date(task.completion_date);
        return completionDate >= dayStart && completionDate <= dayEnd;
      });

      if (tasksCompletedToday.length > 0) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }

    return streak;
  };

  const calculateWeeklyStreak = (completedTasks: Task[]): number => {
    const today = new Date();
    let streak = 0;
    let currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week

    while (true) {
      const weekStart = new Date(currentWeekStart);
      weekStart.setHours(0, 0, 0, 0);
      const weekEnd = new Date(currentWeekStart);
      weekEnd.setDate(weekEnd.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);

      const tasksCompletedThisWeek = completedTasks.filter(task => {
        if (!task.completion_date) return false;
        const completionDate = new Date(task.completion_date);
        return completionDate >= weekStart && completionDate <= weekEnd;
      });

      if (tasksCompletedThisWeek.length > 0) {
        streak++;
        currentWeekStart.setDate(currentWeekStart.getDate() - 7);
      } else {
        break;
      }
    }

    return streak;
  };

  return {
    scheduleTaskReminder,
    cancelTaskReminders,
    showTaskCompletionNotification,
    showProductivityMilestone,
    checkOverdueTasks,
    scheduleAllReminders,
  };
};
