import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
  TextInput,
  FlatList,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  ArrowLeft,
  Shield,
  Smartphone,
  Globe,
  Plus,
  Trash2,
  Settings,
  Clock,
  Zap,
  Search,
  Check,
} from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { installedAppsService, InstalledApp } from '@/services/installedAppsService';
import { mobileAppMonitoringService } from '@/services/mobileAppMonitoringService';
import { IOSDistractionSettings } from '@/components/IOSDistractionSettings';
import { Theme } from '@/types/theme';

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.secondary,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
  },
  backButton: {
    padding: 8,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.background.tertiary,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.accent.primary,
  },
  settingCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    ...theme.shadows.md,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 16,
  },
  settingText: {
    marginLeft: 12,
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    lineHeight: 20,
  },
  addForm: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    ...theme.shadows.md,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.primary,
    backgroundColor: theme.colors.interactive.input.background,
    marginBottom: 12,
  },
  addFormButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  formButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: theme.colors.background.tertiary,
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.secondary,
  },
  addFormButton: {
    backgroundColor: theme.colors.interactive.button.background,
  },
  addButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.interactive.button.text,
  },
  itemCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...theme.shadows.md,
  },
  itemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  itemText: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 2,
  },
  itemCategory: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
  },
  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  itemSwitch: {
    transform: [{ scaleX: 0.9 }, { scaleY: 0.9 }],
  },
  removeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: theme.colors.background.tertiary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: theme.colors.background.secondary,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.primary,
  },
  suggestionsContainer: {
    marginBottom: 16,
    backgroundColor: theme.colors.background.tertiary,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
  },
  suggestionsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.secondary,
    marginBottom: 12,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
  },
  suggestionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.primary,
    marginBottom: 2,
  },
  suggestionPackage: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
  },
  formHelpText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  loadingContainer: {
    padding: 16,
    backgroundColor: theme.colors.background.tertiary,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  errorContainer: {
    padding: 16,
    backgroundColor: theme.colors.status.error,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.status.error,
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.inverse,
  },
  statusIndicator: {
    padding: 4,
  },
});

export default function DistractionSettingsScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const {
    blockedApps,
    blockedWebsites,
    blockingSettings,
    updateBlockingSettings,
    updateBlockedApp,
    updateBlockedWebsite,
    addBlockedApp,
    addBlockedWebsite,
    removeBlockedApp,
    removeBlockedWebsite,
    isLoading,
    iosBlockingState,
  } = useDistractionBlocking();

  const [showAddApp, setShowAddApp] = useState(false);
  const [showAddWebsite, setShowAddWebsite] = useState(false);
  const [newAppName, setNewAppName] = useState('');
  const [newWebsiteName, setNewWebsiteName] = useState('');
  const [newWebsiteUrl, setNewWebsiteUrl] = useState('');
  const [appSearchQuery, setAppSearchQuery] = useState('');
  const [suggestedApps, setSuggestedApps] = useState<InstalledApp[]>([]);
  const [showAppSuggestions, setShowAppSuggestions] = useState(false);
  const [installedApps, setInstalledApps] = useState<InstalledApp[]>([]);
  const [isLoadingApps, setIsLoadingApps] = useState(false);
  const [appLoadError, setAppLoadError] = useState<string | null>(null);
  const [showIOSSettings, setShowIOSSettings] = useState(false);

  // Load installed apps and suggestions on component mount
  useEffect(() => {
    loadInstalledApps();
    loadSuggestedApps();
  }, []);

  const loadSuggestedApps = () => {
    try {
      const suggestions = mobileAppMonitoringService.getSuggestedBlockedApps();
      setSuggestedApps(suggestions.map(app => ({
        appName: app.name,
        packageName: app.packageName,
        icon: '📱', // We don't have icons for suggested apps
        category: app.category,
      })));
    } catch (error) {
      console.error('Error loading suggested apps:', error);
    }
  };

  // Load app suggestions when search query changes
  useEffect(() => {
    if (appSearchQuery.length > 0) {
      searchApps(appSearchQuery);
    } else {
      setSuggestedApps([]);
      setShowAppSuggestions(false);
    }
  }, [appSearchQuery]);

  const loadInstalledApps = async () => {
    setIsLoadingApps(true);
    setAppLoadError(null);
    try {
      const apps = await installedAppsService.getInstalledApps();
      setInstalledApps(apps);
    } catch (error) {
      console.warn('Failed to load installed apps:', error);
      setAppLoadError('Failed to load installed apps. Using common apps instead.');
    } finally {
      setIsLoadingApps(false);
    }
  };

  const searchApps = async (query: string) => {
    try {
      const results = await installedAppsService.searchApps(query);
      setSuggestedApps(results.slice(0, 10)); // Limit to 10 suggestions
      setShowAppSuggestions(results.length > 0);
    } catch (error) {
      console.warn('Failed to search apps:', error);
      setSuggestedApps([]);
      setShowAppSuggestions(false);
    }
  };

  const getPopularAppSuggestions = async () => {
    try {
      const socialApps = await installedAppsService.getAppSuggestionsByCategory('social');
      const entertainmentApps = await installedAppsService.getAppSuggestionsByCategory('entertainment');
      const combined = [...socialApps.slice(0, 4), ...entertainmentApps.slice(0, 4)];
      setSuggestedApps(combined);
      setShowAppSuggestions(combined.length > 0);
    } catch (error) {
      console.warn('Failed to get popular apps:', error);
      // Fallback to first few installed apps
      const fallback = installedApps.slice(0, 8);
      setSuggestedApps(fallback);
      setShowAppSuggestions(fallback.length > 0);
    }
  };

  const handleToggleBlocking = async (enabled: boolean) => {
    try {
      await updateBlockingSettings({ isEnabled: enabled });
    } catch (error) {
      Alert.alert('Error', 'Failed to update blocking settings');
    }
  };

  const handleToggleStrictMode = async (enabled: boolean) => {
    try {
      await updateBlockingSettings({ strictMode: enabled });
    } catch (error) {
      Alert.alert('Error', 'Failed to update strict mode');
    }
  };

  const handleToggleAppBlocking = async (appId: string, isBlocked: boolean) => {
    try {
      await updateBlockedApp(appId, { isBlocked });
    } catch (error) {
      Alert.alert('Error', 'Failed to update app blocking');
    }
  };

  const handleToggleWebsiteBlocking = async (websiteId: string, isBlocked: boolean) => {
    try {
      await updateBlockedWebsite(websiteId, { isBlocked });
    } catch (error) {
      Alert.alert('Error', 'Failed to update website blocking');
    }
  };

  const handleAddApp = async (suggestedApp?: InstalledApp) => {
    const appName = suggestedApp ? suggestedApp.appName : newAppName.trim();
    const packageName = suggestedApp ? suggestedApp.packageName : `com.${newAppName.trim().toLowerCase().replace(/\s+/g, '')}.app`;
    const icon = suggestedApp ? suggestedApp.icon : '📱';
    const category = suggestedApp ? suggestedApp.category : 'other';

    if (!appName) {
      Alert.alert('Error', 'Please enter an app name or select from suggestions');
      return;
    }

    try {
      await addBlockedApp({
        name: appName,
        packageName: packageName,
        icon: icon || '📱',
        category: category || 'other',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      });
      setNewAppName('');
      setAppSearchQuery('');
      setSuggestedApps([]);
      setShowAppSuggestions(false);
      setShowAddApp(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to add app');
    }
  };

  const handleAddWebsite = async () => {
    if (!newWebsiteName.trim() || !newWebsiteUrl.trim()) {
      Alert.alert('Error', 'Please enter both website name and URL');
      return;
    }

    try {
      const url = newWebsiteUrl.startsWith('http') ? newWebsiteUrl : `https://${newWebsiteUrl}`;
      const domain = new URL(url).hostname.replace('www.', '');
      
      await addBlockedWebsite({
        name: newWebsiteName.trim(),
        url,
        domain,
        category: 'other',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      });
      setNewWebsiteName('');
      setNewWebsiteUrl('');
      setShowAddWebsite(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to add website. Please check the URL format.');
    }
  };

  const handleRemoveApp = async (appId: string, appName: string) => {
    Alert.alert(
      'Remove App',
      `Are you sure you want to remove ${appName} from the blocked list?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeBlockedApp(appId);
            } catch (error) {
              Alert.alert('Error', 'Failed to remove app');
            }
          },
        },
      ]
    );
  };

  const handleRemoveWebsite = async (websiteId: string, websiteName: string) => {
    Alert.alert(
      'Remove Website',
      `Are you sure you want to remove ${websiteName} from the blocked list?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeBlockedWebsite(websiteId);
            } catch (error) {
              Alert.alert('Error', 'Failed to remove website');
            }
          },
        },
      ]
    );
  };

  if (isLoading || !blockingSettings) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={theme.colors.accent.primary} />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollContent}
      >
      <View style={styles.headerGradient}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Distraction Blocking</Text>
          <View style={styles.placeholder} />
        </View>
      </View>

      {/* Main Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>General Settings</Text>
        
        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Shield size={24} color="#10B981" />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Enable Blocking</Text>
                <Text style={styles.settingDescription}>
                  Block distracting apps and websites during focus sessions
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.isEnabled}
              onValueChange={handleToggleBlocking}
              trackColor={{ false: theme.colors.ui.border, true: theme.colors.status.success }}
              thumbColor={theme.colors.text.inverse}
            />
          </View>
        </View>

        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Zap size={24} color={theme.colors.status.warning} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Strict Mode</Text>
                <Text style={styles.settingDescription}>
                  Prevent disabling blocking during active focus sessions
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.strictMode}
              onValueChange={handleToggleStrictMode}
              trackColor={{ false: theme.colors.ui.border, true: theme.colors.status.warning }}
              thumbColor={theme.colors.text.inverse}
            />
          </View>
        </View>

        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Clock size={24} color={theme.colors.accent.primary} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Auto-Start with Timer</Text>
                <Text style={styles.settingDescription}>
                  Automatically enable blocking when any timer is started
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.autoStartWithTimer}
              onValueChange={(value) => updateBlockingSettings({ autoStartWithTimer: value })}
              trackColor={{ false: theme.colors.ui.border, true: theme.colors.accent.primary }}
              thumbColor={theme.colors.text.inverse}
            />
          </View>
        </View>

        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Settings size={24} color={theme.colors.accent.primary} />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Manual Activation</Text>
                <Text style={styles.settingDescription}>
                  Allow manual activation of blocking outside of timer sessions
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.allowManualActivation}
              onValueChange={(value) => updateBlockingSettings({ allowManualActivation: value })}
              trackColor={{ false: theme.colors.ui.border, true: theme.colors.accent.primary }}
              thumbColor={theme.colors.text.inverse}
            />
          </View>
        </View>
      </View>

      {/* iOS-specific Settings */}
      {Platform.OS === 'ios' && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>iOS App Blocking</Text>

          <View style={styles.settingCard}>
            <TouchableOpacity
              style={styles.settingRow}
              onPress={() => setShowIOSSettings(true)}
            >
              <View style={styles.settingInfo}>
                <Smartphone size={24} color={theme.colors.accent.primary} />
                <View style={styles.settingText}>
                  <Text style={styles.settingLabel}>Screen Time Integration</Text>
                  <Text style={styles.settingDescription}>
                    {iosBlockingState.isAuthorized
                      ? `Configure app blocking with Apple's Screen Time`
                      : 'Set up Screen Time permissions to block apps'
                    }
                  </Text>
                </View>
              </View>
              <View style={styles.statusIndicator}>
                {iosBlockingState.isAuthorized ? (
                  <Check size={20} color={theme.colors.status.success} />
                ) : (
                  <Settings size={20} color={theme.colors.text.secondary} />
                )}
              </View>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Blocked Apps */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Blocked Apps</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              setShowAddApp(true);
              getPopularAppSuggestions();
            }}
          >
            <Plus size={20} color={theme.colors.accent.primary} />
          </TouchableOpacity>
        </View>

        {showAddApp && (
          <View style={styles.addForm}>
            <View style={styles.searchContainer}>
              <Search size={20} color={theme.colors.text.secondary} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search for apps (e.g., Instagram, Facebook)"
                value={appSearchQuery}
                onChangeText={setAppSearchQuery}
                autoCapitalize="words"
                onFocus={() => {
                  if (suggestedApps.length > 0) {
                    setShowAppSuggestions(true);
                  }
                }}
              />
            </View>

            {/* Loading State */}
            {isLoadingApps && (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Loading installed apps...</Text>
              </View>
            )}

            {/* Error State */}
            {appLoadError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{appLoadError}</Text>
              </View>
            )}

            {/* App Suggestions */}
            {showAppSuggestions && suggestedApps.length > 0 && (
              <View style={styles.suggestionsContainer}>
                <Text style={styles.suggestionsTitle}>Suggested Apps:</Text>
                <FlatList
                  data={suggestedApps}
                  keyExtractor={(item) => item.packageName}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.suggestionItem}
                      onPress={() => handleAddApp(item)}
                    >
                      <Text style={styles.suggestionIcon}>{item.icon}</Text>
                      <View style={styles.suggestionInfo}>
                        <Text style={styles.suggestionName}>{item.appName}</Text>
                        <Text style={styles.suggestionPackage}>{item.packageName}</Text>
                      </View>
                      <Plus size={16} color={theme.colors.status.success} />
                    </TouchableOpacity>
                  )}
                  style={styles.suggestionsList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                />
              </View>
            )}

            {/* Manual Entry Fallback */}
            <TextInput
              style={styles.textInput}
              placeholder="Or enter app name manually"
              value={newAppName}
              onChangeText={setNewAppName}
              autoCapitalize="words"
            />

            <View style={styles.addFormButtons}>
              <TouchableOpacity
                style={[styles.formButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddApp(false);
                  setNewAppName('');
                  setAppSearchQuery('');
                  setSuggestedApps([]);
                  setShowAppSuggestions(false);
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.formButton, styles.addFormButton]}
                onPress={() => handleAddApp()}
              >
                <Text style={styles.addButtonText}>Add App</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {blockedApps.map((app) => (
          <View key={app.id} style={styles.itemCard}>
            <View style={styles.itemInfo}>
              <Text style={styles.itemIcon}>{app.icon}</Text>
              <View style={styles.itemText}>
                <Text style={styles.itemName}>{app.name}</Text>
                <Text style={styles.itemCategory}>{app.category}</Text>
              </View>
            </View>
            <View style={styles.itemActions}>
              <Switch
                value={app.isBlocked}
                onValueChange={(value) => handleToggleAppBlocking(app.id, value)}
                trackColor={{ false: theme.colors.ui.border, true: theme.colors.status.error }}
                thumbColor={theme.colors.text.inverse}
                style={styles.itemSwitch}
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveApp(app.id, app.name)}
              >
                <Trash2 size={18} color={theme.colors.status.error} />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>

      {/* Blocked Websites */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Blocked Websites</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddWebsite(true)}
          >
            <Plus size={20} color={theme.colors.accent.primary} />
          </TouchableOpacity>
        </View>

        {showAddWebsite && (
          <View style={styles.addForm}>
            <Text style={styles.formHelpText}>
              Add websites you want to block during focus sessions
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder="Website name (e.g., Facebook, Instagram, YouTube)"
              value={newWebsiteName}
              onChangeText={setNewWebsiteName}
              autoCapitalize="words"
            />
            <TextInput
              style={styles.textInput}
              placeholder="Website URL (e.g., facebook.com, instagram.com)"
              value={newWebsiteUrl}
              onChangeText={setNewWebsiteUrl}
              autoCapitalize="none"
              keyboardType="url"
              autoCorrect={false}
            />
            <Text style={styles.formHelpText}>
              💡 Tip: Enter just the domain (e.g., "youtube.com") to block the entire site
            </Text>
            <View style={styles.addFormButtons}>
              <TouchableOpacity
                style={[styles.formButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddWebsite(false);
                  setNewWebsiteName('');
                  setNewWebsiteUrl('');
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.formButton, styles.addFormButton]}
                onPress={handleAddWebsite}
              >
                <Text style={styles.addButtonText}>Add Website</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {blockedWebsites.map((website) => (
          <View key={website.id} style={styles.itemCard}>
            <View style={styles.itemInfo}>
              <Globe size={24} color={theme.colors.text.secondary} />
              <View style={styles.itemText}>
                <Text style={styles.itemName}>{website.name}</Text>
                <Text style={styles.itemCategory}>{website.domain}</Text>
              </View>
            </View>
            <View style={styles.itemActions}>
              <Switch
                value={website.isBlocked}
                onValueChange={(value) => handleToggleWebsiteBlocking(website.id, value)}
                trackColor={{ false: theme.colors.ui.border, true: theme.colors.status.error }}
                thumbColor={theme.colors.text.inverse}
                style={styles.itemSwitch}
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveWebsite(website.id, website.name)}
              >
                <Trash2 size={18} color={theme.colors.status.error} />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>
      </ScrollView>

      {/* iOS Settings Modal */}
      <Modal
        visible={showIOSSettings}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowIOSSettings(false)}
      >
        <IOSDistractionSettings onClose={() => setShowIOSSettings(false)} />
      </Modal>
    </KeyboardAvoidingView>
  );
}
