import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  Shield,
  Volume2,
  Smartphone,
  Zap,
  Target,
} from 'lucide-react-native';

import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { IconButton, Card } from '@/components/ui';
import DynamicIslandSelector from '@/components/ui/DynamicIslandSelector';
import { useTimer } from '@/hooks/useTimer';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'expo-router';
import BlockingNotifications, { NotificationData } from '@/components/BlockingNotifications';
import DistractionBlockingDemo from '@/components/DistractionBlockingDemo';
import { SessionCompletionFlow } from '@/components/session/SessionCompletionFlow';
import { createTimerStyles } from '@/styles/createTimerStyles';
import { useMemo } from 'react';
import { TimerSession, SessionSummary } from '@/types/app';

interface SoundOption {
  id: string;
  name: string;
  icon: string;
  active: boolean;
}

export default function TimerScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const { user } = useAuth();
  const styles = useMemo(() => createTimerStyles(theme), [theme]);
  const { colors } = theme;
  const {
    isRunning,
    time,
    mode,
    currentSubject,
    pomodoroPhase,
    pomodoroSession,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    switchMode,
    setCurrentSubject,
    formatTime,
    getTotalTimeToday,
  } = useTimer();

  // Use distraction blocking hook
  const {
    isBlockingEnabled,
    isActivelyBlocking,
    currentBlockingReason,
    stats,
    recentlyBlockedApps,
    setFocusContext,
    recordDistractionAttempt,
    startTimerBlocking,
    stopTimerBlocking,
    toggleManualBlocking,
    isLoading: isBlockingLoading,
  } = useDistractionBlocking();

  // Local state
  const [showDemo, setShowDemo] = useState(false);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // Focus sounds state
  const [soundOptions, setSoundOptions] = useState<SoundOption[]>([
    { id: '1', name: 'Rain', icon: '🌧️', active: false },
    { id: '2', name: 'Forest', icon: '🌲', active: false },
    { id: '3', name: 'Ocean', icon: '🌊', active: false },
    { id: '4', name: 'Coffee Shop', icon: '☕', active: false },
  ]);

  // Session completion flow state
  const [showSessionCompletion, setShowSessionCompletion] = useState(false);
  const [completionType, setCompletionType] = useState<'complete' | 'pause' | 'cancel'>('complete');
  const [currentSession, setCurrentSession] = useState<TimerSession | null>(null);

  // Update distraction blocking context when timer state changes
  useEffect(() => {
    if (isRunning) {
      // Start timer-based blocking
      startTimerBlocking();

      // Set appropriate focus context
      if (mode === 'pomodoro') {
        setFocusContext(pomodoroPhase === 'work' ? 'focus' : 'break');
      } else {
        // For stopwatch, always use focus context
        setFocusContext('focus');
      }
    } else {
      // Stop timer-based blocking
      stopTimerBlocking();
      setFocusContext('normal');
    }
  }, [isRunning, mode, pomodoroPhase, setFocusContext, startTimerBlocking, stopTimerBlocking]);

  // Listen for distraction attempts to show in-app notifications
  useEffect(() => {
    if (!isRunning) return;

    const { mobileAppMonitoringService } = require('@/services/mobileAppMonitoringService');

    const unsubscribe = mobileAppMonitoringService.onDistractionAttempt((attempt: any) => {
      if (attempt.blocked) {
        addNotification({
          type: 'warning',
          title: '🚫 App Blocked',
          message: `${attempt.appName} is blocked during focus time. Stay focused!`,
          duration: 4000,
        });
      }
    });

    return unsubscribe;
  }, [isRunning]);

  const addNotification = (notification: Omit<NotificationData, 'id'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString(),
    };
    setNotifications(prev => [...prev, newNotification]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const toggleSound = (soundId: string) => {
    setSoundOptions(prev =>
      prev.map(sound =>
        sound.id === soundId
          ? { ...sound, active: !sound.active }
          : { ...sound, active: false }
      )
    );
  };

  const handleModeSwitch = (newMode: 'stopwatch' | 'pomodoro') => {
    if (isRunning) {
      stopTimer();
    }
    switchMode(newMode);
  };

  const handleStartTimer = () => {
    if (!currentSubject) {
      Alert.alert(
        'No Subject Selected',
        'Please select a subject before starting the timer.'
      );
      return;
    }

    // Create session data when starting timer
    const sessionData: TimerSession = {
      id: Date.now().toString(),
      userId: user?.id || '',
      subject: currentSubject.name,
      subjectId: currentSubject.id,
      subjectColor: currentSubject.color,
      taskName: '',
      taskType: 'General Study',
      mode,
      phase: mode === 'pomodoro' ? pomodoroPhase : undefined,
      startTime: new Date(),
      endTime: null,
      duration: 0,
      completed: false,
      date: new Date().toISOString().split('T')[0],
      notes: '',
      feedback: '',
      productivityRating: 0,
    };

    setCurrentSession(sessionData);
    startTimer();

    // Show focus notification when timer starts (automatic focus mode)
    addNotification({
      type: 'info',
      title: '🎯 Focus Mode Activated',
      message: 'Timer started! Distracting apps are now blocked.',
      duration: 3000,
    });
  };

  // Session completion handlers
  const handleStopTimer = () => {
    if (currentSession && time > 0) {
      // Update session with current duration
      const updatedSession = {
        ...currentSession,
        duration: time,
        endTime: new Date(),
      };
      setCurrentSession(updatedSession);
      setCompletionType('complete');
      setShowSessionCompletion(true);
    } else {
      // No session to save, just stop timer
      stopTimer();
      setCurrentSession(null);
    }
  };

  const handlePauseTimer = () => {
    if (currentSession && time > 0) {
      // Update session with current duration
      const updatedSession = {
        ...currentSession,
        duration: time,
      };
      setCurrentSession(updatedSession);
      setCompletionType('pause');
      setShowSessionCompletion(true);
    } else {
      // No session to save, just pause timer
      pauseTimer();
    }
  };

  const handleResetTimer = () => {
    if (isRunning && currentSession && time > 0) {
      // Ask user if they want to save the session before resetting
      Alert.alert(
        'Reset Timer',
        'Do you want to save your current session before resetting?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Reset Without Saving',
            style: 'destructive',
            onPress: () => {
              resetTimer();
              setCurrentSession(null);
            },
          },
          {
            text: 'Save & Reset',
            onPress: () => {
              const updatedSession = {
                ...currentSession,
                duration: time,
                endTime: new Date(),
              };
              setCurrentSession(updatedSession);
              setCompletionType('complete');
              setShowSessionCompletion(true);
            },
          },
        ]
      );
    } else {
      resetTimer();
      setCurrentSession(null);
    }
  };

  const handleSessionSaved = (session: TimerSession) => {
    setShowSessionCompletion(false);
    setCurrentSession(null);

    if (completionType === 'complete') {
      stopTimer();
      addNotification({
        type: 'success',
        title: '✅ Session Completed!',
        message: `Great work! You studied for ${formatTime(session.duration)}.`,
        duration: 4000,
      });
    } else if (completionType === 'pause') {
      pauseTimer();
      addNotification({
        type: 'info',
        title: '⏸️ Session Paused',
        message: 'Your progress has been saved. Resume when ready!',
        duration: 3000,
      });
    }
  };

  const getTimerProgress = () => {
    if (mode === 'stopwatch') return 0;

    const totalTime = pomodoroPhase === 'work' ? 25 * 60 : 5 * 60; // Simplified for demo
    return ((totalTime - time) / totalTime) * 100;
  };

  const getPhaseText = () => {
    if (mode === 'stopwatch') {
      return isRunning ? 'Focus Time' : 'Stopwatch';
    }
    return pomodoroPhase === 'work' ? 'Focus Time' : 'Break Time';
  };

  const getPhaseColor = (): string => {
    if (mode === 'stopwatch') {
      return isRunning ? colors.accent.primary : colors.interactive.button.background;
    }
    return pomodoroPhase === 'work' ? colors.accent.primary : colors.status.success;
  };

  const getPhaseIcon = () => {
    if (mode === 'stopwatch') {
      return isRunning ? '🎯' : '⏱️';
    }
    return pomodoroPhase === 'work' ? '🎯' : '☕';
  };

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <View style={styles.header}>
          <IsotopeLogo size="medium" />
          <View style={styles.headerActions}>
            {isRunning && (
              <TouchableOpacity
                style={[styles.settingsButton, { backgroundColor: colors.background.secondary }]}
                onPress={() => setShowDemo(!showDemo)}
              >
                <Text style={{ color: colors.text.primary }}>
                  {showDemo ? 'Hide Demo' : 'Test Blocking'}
                </Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => router.push('/distraction-settings')}
            >
              <Shield size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Mode Selector */}
        <DynamicIslandSelector
          options={[
            { key: 'stopwatch', label: 'Stopwatch' },
            { key: 'pomodoro', label: 'Pomodoro' },
          ]}
          selectedOption={mode}
          onSelectOption={(newMode) => handleModeSwitch(newMode as 'stopwatch' | 'pomodoro')}
        />

        {/* Subject Picker */}
        <View style={styles.subjectSection}>
          <SubjectPicker
            selectedSubject={currentSubject}
            onSelectSubject={setCurrentSubject}
          />
        </View>

        {/* Focus Stats */}
        {isRunning && (
          <View style={styles.statsSection}>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Zap size={20} color={colors.accent.primary} />
                <Text style={styles.statValue}>
                  {mode === 'pomodoro' ? pomodoroSession : '1'}
                </Text>
                <Text style={styles.statLabel}>Session</Text>
              </View>
              <View style={styles.statCard}>
                <Target size={20} color={colors.status.success} />
                <Text style={styles.statValue}>
                  {mode === 'pomodoro' ? '4' : '∞'}
                </Text>
                <Text style={styles.statLabel}>Goal</Text>
              </View>
              <View style={styles.statCard}>
                <Smartphone size={20} color={colors.status.error} />
                <Text style={styles.statValue}>
                  {stats.blockedToday}
                </Text>
                <Text style={styles.statLabel}>Blocked</Text>
              </View>
            </View>
          </View>
        )}

        {/* Main Timer */}
        <View style={styles.timerSection}>
          <View style={styles.timerCard}>
            <View style={styles.timerHeader}>
              <LinearGradient
                colors={[getPhaseColor(), getPhaseColor()]}
                style={styles.phaseIndicator}
              >
                <Text style={styles.phaseIcon}>{getPhaseIcon()}</Text>
              </LinearGradient>
              <View style={styles.timerInfo}>
                <Text style={styles.timerLabel}>{getPhaseText()}</Text>
                {mode === 'pomodoro' && (
                  <Text style={styles.cycleText}>
                    Session {pomodoroSession}
                  </Text>
                )}
              </View>
            </View>
            <View style={styles.timerDisplay}>
              <Text style={styles.timerText}>{formatTime(time)}</Text>
              {mode === 'pomodoro' && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressTrack}>
                    <LinearGradient
                      colors={[getPhaseColor(), getPhaseColor()]}
                      style={[
                        styles.progressFill,
                        { width: `${getTimerProgress()}%` },
                      ]}
                    />
                  </View>
                </View>
              )}
            </View>
          </View>

          {/* Timer Controls */}
          <View style={styles.timerControls}>
            <TouchableOpacity style={styles.controlButton} onPress={handleResetTimer}>
              <RotateCcw size={28} color={colors.text.secondary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.controlButton, styles.playButton]}
              onPress={isRunning ? handlePauseTimer : handleStartTimer}
            >
              {isRunning ? <Pause size={32} color="#FFFFFF" /> : <Play size={32} color="#FFFFFF" />}
            </TouchableOpacity>
            <TouchableOpacity style={styles.controlButton} onPress={handleStopTimer}>
              <Square size={28} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Focus Sounds - Show when timer is running */}
        {isRunning && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Focus Sounds</Text>
            <View style={styles.soundGrid}>
              {soundOptions.map((sound) => (
                <TouchableOpacity
                  key={sound.id}
                  style={[
                    styles.soundCard,
                    sound.active && { backgroundColor: colors.accent.primary },
                  ]}
                  onPress={() => toggleSound(sound.id)}
                >
                  <Text style={styles.soundIcon}>{sound.icon}</Text>
                  <Text style={[styles.soundName, { color: sound.active ? colors.text.primary : colors.text.secondary }]}>
                    {sound.name}
                  </Text>
                  {sound.active && (
                    <View style={[styles.activeBadge, { backgroundColor: colors.accent.primary }]}>
                      <Volume2 size={16} color="#FFFFFF" />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Distraction Blocking */}
        <View style={styles.section}>
          <View style={styles.blockingSectionHeader}>
            <Text style={styles.sectionTitle}>Distraction Blocking</Text>
            <TouchableOpacity
              style={[styles.settingsButton, { backgroundColor: colors.background.secondary }]}
              onPress={() => router.push('/distraction-settings')}
            >
              <Shield size={20} color={colors.text.secondary} />
            </TouchableOpacity>
          </View>
          <View style={styles.blockingCard}>
            <View style={styles.blockingHeader}>
              <Shield size={24} color={isActivelyBlocking ? colors.status.success : colors.text.secondary} />
              <Text style={styles.blockingTitle}>
                {isActivelyBlocking ? `${currentBlockingReason === 'timer' ? 'Timer' : 'Manual'} Blocking` : 'Distraction Blocking'}
              </Text>
              <View style={[styles.statusBadge, { backgroundColor: isActivelyBlocking ? colors.status.success : colors.background.secondary }]}>
                <Text style={[styles.statusText, { color: isActivelyBlocking ? colors.text.primary : colors.text.secondary }]}>
                  {isActivelyBlocking ? "ACTIVE" : "READY"}
                </Text>
              </View>
            </View>
            <Text style={styles.blockingDescription}>
              {isActivelyBlocking
                ? `Distracting apps are currently blocked. ${currentBlockingReason === 'timer' ? 'Timer-based' : 'Manual'} blocking is active!`
                : isBlockingEnabled
                ? "Distraction blocking will automatically activate when you start any timer, or you can start it manually."
                : "Enable distraction blocking in settings to automatically block distracting apps during timer sessions."
              }
            </Text>
            {!isRunning && (
              <TouchableOpacity
                style={[
                  styles.manualBlockingButton,
                  isActivelyBlocking && { backgroundColor: colors.accent.primary }
                ]}
                onPress={toggleManualBlocking}
              >
                <Shield size={20} color={isActivelyBlocking ? colors.text.primary : colors.accent.primary} />
                <Text style={[
                  styles.manualBlockingButtonText,
                  { color: isActivelyBlocking ? colors.text.primary : colors.accent.primary }
                ]}>
                  {isActivelyBlocking ? 'Stop Manual Blocking' : 'Start Manual Blocking'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Today's Progress */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Progress</Text>
          <View style={styles.progressCard}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatTime(getTotalTimeToday())}</Text>
              <Text style={styles.statLabel}>Total Time</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {currentSubject ? currentSubject.name : 'N/A'}
              </Text>
              <Text style={styles.statLabel}>Current Subject</Text>
            </View>
          </View>
        </View>

        {/* Quick Tips */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tips</Text>
          <View style={styles.tipCard}>
            <Text style={styles.tipTitle}>
              {mode === 'stopwatch' ? '⏱️ Stopwatch Mode' : '🍅 Pomodoro Technique'}
            </Text>
            <Text style={styles.tipText}>
              {mode === 'stopwatch'
                ? 'Perfect for open-ended study sessions. Starting the timer automatically enables focus mode to block distractions.'
                : 'Work for 25 minutes, then take a 5-minute break. Focus mode automatically activates during work sessions to block distracting apps.'
              }
            </Text>
          </View>
        </View>

        {/* Demo Section */}
        {isRunning && isBlockingEnabled && (
          <DistractionBlockingDemo isVisible={showDemo} />
        )}
      </ScrollView>

      {/* Notifications */}
      <BlockingNotifications
        notifications={notifications}
        onDismiss={removeNotification}
      />

      {/* Session Completion Flow */}
      {showSessionCompletion && currentSession && user && (
        <SessionCompletionFlow
          visible={showSessionCompletion}
          onClose={() => setShowSessionCompletion(false)}
          session={currentSession}
          userId={user.id}
          completionType={completionType}
          onSessionSaved={handleSessionSaved}
        />
      )}
    </View>
  );
}
