import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Heart,
  Star,
  Settings,
  Download,
  Trash2,
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  Phone,
  Calendar,
  Clock,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import {
  Button,
  IconButton,
  FloatingActionButton,
  Input,
  TextArea,
  Switch,
  ToggleButton,
  Card,
  StatCard,
  ActionCard,
  Modal,
  ConfirmationModal,
  BottomSheetModal,
} from '@/components/ui';

export default function UIDemo() {
  const { theme } = useTheme();
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [bottomSheetVisible, setBottomSheetVisible] = useState(false);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      <LinearGradient
        colors={[theme.colors.background.primary, theme.colors.background.secondary]}
        style={styles.header}
      >
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          UI Component Demo
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
          IsotopeAI Design System
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Buttons Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Buttons
          </Text>
          
          <View style={styles.buttonRow}>
            <Button
              title="Primary"
              onPress={() => Alert.alert('Primary Button')}
              variant="primary"
              style={styles.button}
            />
            <Button
              title="Secondary"
              onPress={() => Alert.alert('Secondary Button')}
              variant="secondary"
              style={styles.button}
            />
          </View>

          <View style={styles.buttonRow}>
            <Button
              title="Outline"
              onPress={() => Alert.alert('Outline Button')}
              variant="outline"
              style={styles.button}
            />
            <Button
              title="Danger"
              onPress={() => Alert.alert('Danger Button')}
              variant="danger"
              style={styles.button}
            />
          </View>

          <Button
            title="Gradient Button"
            onPress={() => Alert.alert('Gradient Button')}
            variant="primary"
            gradient
            icon={<Star size={20} color="#FFFFFF" />}
            iconPosition="right"
            fullWidth
            style={styles.fullButton}
          />

          <Button
            title="Loading..."
            onPress={() => {}}
            variant="primary"
            loading
            fullWidth
            style={styles.fullButton}
          />
        </View>

        {/* Icon Buttons Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Icon Buttons
          </Text>
          
          <View style={styles.iconButtonRow}>
            <IconButton
              icon={<Heart size={24} color={theme.colors.accent.primary} />}
              onPress={() => Alert.alert('Heart')}
              variant="secondary"
            />
            <IconButton
              icon={<Star size={24} color="#FFFFFF" />}
              onPress={() => Alert.alert('Star')}
              variant="primary"
              gradient
            />
            <IconButton
              icon={<Settings size={24} color={theme.colors.text.secondary} />}
              onPress={() => Alert.alert('Settings')}
              variant="ghost"
            />
            <FloatingActionButton
              icon={<Download size={24} color="#FFFFFF" />}
              onPress={() => Alert.alert('FAB')}
            />
          </View>
        </View>

        {/* Input Fields Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Input Fields
          </Text>
          
          <Input
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            leftIcon={<Mail size={20} color={theme.colors.text.secondary} />}
            keyboardType="email-address"
            style={styles.input}
          />

          <Input
            label="Password"
            placeholder="Enter your password"
            value={password}
            onChangeText={setPassword}
            leftIcon={<Lock size={20} color={theme.colors.text.secondary} />}
            rightIcon={
              showPassword ? (
                <EyeOff size={20} color={theme.colors.text.secondary} />
              ) : (
                <Eye size={20} color={theme.colors.text.secondary} />
              )
            }
            onRightIconPress={() => setShowPassword(!showPassword)}
            secureTextEntry={!showPassword}
            style={styles.input}
          />

          <Input
            label="Phone (Filled Variant)"
            placeholder="Enter your phone"
            leftIcon={<Phone size={20} color={theme.colors.text.secondary} />}
            variant="filled"
            style={styles.input}
          />

          <TextArea
            label="Message"
            placeholder="Enter your message..."
            value={message}
            onChangeText={setMessage}
            rows={4}
            style={styles.input}
          />
        </View>

        {/* Switches Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Switches & Toggles
          </Text>
          
          <Switch
            value={notifications}
            onValueChange={setNotifications}
            label="Push Notifications"
            description="Receive notifications about important updates"
            style={styles.switch}
          />

          <Switch
            value={darkMode}
            onValueChange={setDarkMode}
            label="Dark Mode"
            description="Switch between light and dark themes"
            size="lg"
            style={styles.switch}
          />

          <ToggleButton
            value={darkMode}
            onValueChange={setDarkMode}
            leftLabel="Light"
            rightLabel="Dark"
            style={styles.toggle}
          />
        </View>

        {/* Cards Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Cards
          </Text>
          
          <View style={styles.cardRow}>
            <StatCard
              title="Total Tasks"
              value="24"
              icon={<Calendar size={20} color={theme.colors.accent.primary} />}
              trend={{ value: 12, isPositive: true }}
              style={styles.statCard}
            />
            <StatCard
              title="Time Spent"
              value="4.2h"
              icon={<Clock size={20} color={theme.colors.status.success} />}
              trend={{ value: 8, isPositive: false }}
              style={styles.statCard}
            />
          </View>

          <ActionCard
            title="Export Data"
            description="Download your data as a backup file"
            icon={<Download size={24} color={theme.colors.accent.primary} />}
            onPress={() => Alert.alert('Export Data')}
            variant="primary"
            style={styles.actionCard}
          />

          <ActionCard
            title="Delete Account"
            description="Permanently delete your account and all data"
            icon={<Trash2 size={24} color={theme.colors.status.error} />}
            onPress={() => setConfirmVisible(true)}
            variant="danger"
            style={styles.actionCard}
          />

          <Card
            variant="elevated"
            gradient
            onPress={() => Alert.alert('Gradient Card')}
            style={styles.gradientCard}
          >
            <Text style={[styles.cardTitle, { color: theme.colors.text.inverse }]}>
              Premium Feature
            </Text>
            <Text style={[styles.cardDescription, { color: theme.colors.text.inverse }]}>
              Unlock advanced analytics and insights
            </Text>
          </Card>
        </View>

        {/* Modal Buttons Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
            Modals
          </Text>
          
          <View style={styles.buttonRow}>
            <Button
              title="Show Modal"
              onPress={() => setModalVisible(true)}
              variant="outline"
              style={styles.button}
            />
            <Button
              title="Bottom Sheet"
              onPress={() => setBottomSheetVisible(true)}
              variant="outline"
              style={styles.button}
            />
          </View>
        </View>
      </ScrollView>

      {/* Modals */}
      <Modal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        title="Sample Modal"
        size="md"
      >
        <Text style={[styles.modalText, { color: theme.colors.text.primary }]}>
          This is a sample modal with the new design system. It includes proper animations,
          theming, and accessibility features.
        </Text>
        <Button
          title="Close Modal"
          onPress={() => setModalVisible(false)}
          variant="primary"
          fullWidth
          style={{ marginTop: 16 }}
        />
      </Modal>

      <ConfirmationModal
        visible={confirmVisible}
        onClose={() => setConfirmVisible(false)}
        onConfirm={() => Alert.alert('Account Deleted')}
        title="Delete Account"
        message="Are you sure you want to delete your account? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
      />

      <BottomSheetModal
        visible={bottomSheetVisible}
        onClose={() => setBottomSheetVisible(false)}
        title="Bottom Sheet"
        height={400}
      >
        <Text style={[styles.modalText, { color: theme.colors.text.primary }]}>
          This is a bottom sheet modal that slides up from the bottom of the screen.
          Perfect for mobile interactions and quick actions.
        </Text>
        <Button
          title="Action Button"
          onPress={() => Alert.alert('Action performed')}
          variant="primary"
          fullWidth
          style={{ marginTop: 16 }}
        />
      </BottomSheetModal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  button: {
    flex: 1,
  },
  fullButton: {
    marginBottom: 12,
  },
  iconButtonRow: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'center',
  },
  input: {
    marginBottom: 16,
  },
  switch: {
    marginBottom: 16,
  },
  toggle: {
    marginBottom: 16,
  },
  cardRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
  },
  actionCard: {
    marginBottom: 16,
  },
  gradientCard: {
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
  modalText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
  },
});
