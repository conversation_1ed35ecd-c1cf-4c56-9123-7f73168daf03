import { Platform, Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import * as Notifications from 'expo-notifications';
import { Task } from '@/types/app';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface NotificationOptions {
  title: string;
  message: string;
  type?: 'success' | 'warning' | 'error' | 'info';
  duration?: number;
  haptic?: boolean;
}

export interface ScheduledNotificationOptions {
  title: string;
  body: string;
  data?: any;
  trigger: Notifications.NotificationTriggerInput;
  identifier?: string;
}

export interface TaskReminderOptions {
  task: Task;
  reminderType: 'due_soon' | 'overdue' | 'milestone_due' | 'custom';
  customMessage?: string;
}

class NotificationService {
  private isHapticsAvailable = Platform.OS !== 'web';
  private isInitialized = false;

  async showNotification(options: NotificationOptions): Promise<void> {
    const { title, message, type = 'info', haptic = true } = options;

    // Trigger haptic feedback if available and requested
    if (haptic && this.isHapticsAvailable) {
      try {
        switch (type) {
          case 'success':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            break;
          case 'warning':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          case 'error':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          default:
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      } catch (error) {
        console.warn('Haptic feedback failed:', error);
      }
    }

    // Show platform-appropriate notification
    if (Platform.OS === 'web') {
      // For web, use browser notifications if available
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
          body: message,
          icon: '/favicon.png',
        });
      } else {
        // Fallback to alert
        Alert.alert(title, message);
      }
    } else {
      // For mobile, use Alert for now (can be enhanced with push notifications)
      Alert.alert(title, message);
    }
  }

  async showSuccessNotification(message: string): Promise<void> {
    await this.showNotification({
      title: 'Success',
      message,
      type: 'success',
    });
  }

  async showWarningNotification(message: string): Promise<void> {
    await this.showNotification({
      title: 'Warning',
      message,
      type: 'warning',
    });
  }

  async showErrorNotification(message: string): Promise<void> {
    await this.showNotification({
      title: 'Error',
      message,
      type: 'error',
    });
  }

  async showFocusCompleteNotification(sessionDuration: number): Promise<void> {
    const minutes = Math.floor(sessionDuration / 60);
    await this.showNotification({
      title: '🎯 Focus Session Complete!',
      message: `Great job! You focused for ${minutes} minutes. Time for a well-deserved break.`,
      type: 'success',
    });
  }

  async showBreakReminder(): Promise<void> {
    await this.showNotification({
      title: '☕ Break Time!',
      message: 'Take a short break to recharge. You\'ve earned it!',
      type: 'info',
    });
  }

  async showDistractionBlocked(appName: string): Promise<void> {
    await this.showNotification({
      title: '🚫 App Blocked',
      message: `${appName} is blocked during focus time. Stay focused!`,
      type: 'warning',
      haptic: true,
    });
  }

  async showFocusEncouragement(): Promise<void> {
    await this.showNotification({
      title: '💪 Stay Focused!',
      message: 'You\'re doing great! Keep up the momentum.',
      type: 'info',
      haptic: true,
    });
  }

  async showStrictModeBlocking(timeAway: number): Promise<void> {
    const minutes = Math.floor(timeAway / 60000);
    const seconds = Math.floor((timeAway % 60000) / 1000);

    await this.showNotification({
      title: '🛡️ Strict Mode Active',
      message: `You've been away for ${minutes > 0 ? `${minutes}m ` : ''}${seconds}s. Return to maintain your focus streak!`,
      type: 'warning',
      haptic: true,
    });
  }

  async showDistractionDetected(timeAway: number): Promise<void> {
    const minutes = Math.floor(timeAway / 60000);
    const seconds = Math.floor((timeAway % 60000) / 1000);

    await this.showNotification({
      title: '⚠️ Distraction Detected',
      message: `Potential distraction detected after ${minutes > 0 ? `${minutes}m ` : ''}${seconds}s. Stay focused!`,
      type: 'warning',
      haptic: true,
    });
  }

  async showTimerBlockingStarted(): Promise<void> {
    await this.showNotification({
      title: '🎯 Focus Mode Activated',
      message: 'Timer started! Distraction blocking is now active.',
      type: 'success',
      haptic: true,
    });
  }

  async showManualBlockingStarted(): Promise<void> {
    await this.showNotification({
      title: '🔒 Manual Blocking Active',
      message: 'Manual distraction blocking is now active. Stay focused!',
      type: 'info',
      haptic: true,
    });
  }

  async showBlockingDeactivated(): Promise<void> {
    await this.showNotification({
      title: '✅ Blocking Deactivated',
      message: 'Distraction blocking has been turned off.',
      type: 'success',
      haptic: true,
    });
  }

  async showFocusStreak(streakCount: number): Promise<void> {
    await this.showNotification({
      title: '🔥 Focus Streak!',
      message: `Amazing! You've maintained focus for ${streakCount} sessions in a row!`,
      type: 'success',
      haptic: true,
    });
  }

  async initialize(): Promise<boolean> {
    if (this.isInitialized) return true;

    try {
      const hasPermission = await this.requestPermissions();
      if (hasPermission) {
        this.isInitialized = true;
        console.log('📱 Notification service initialized successfully');
      }
      return hasPermission;
    } catch (error) {
      console.error('📱 Failed to initialize notification service:', error);
      return false;
    }
  }

  async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          return permission === 'granted';
        }
        return false;
      } else {
        // For mobile platforms, use expo-notifications
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        return finalStatus === 'granted';
      }
    } catch (error) {
      console.error('📱 Failed to request notification permissions:', error);
      return false;
    }
  }

  // Task Reminder Methods
  async scheduleTaskReminder(options: TaskReminderOptions): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const { task, reminderType, customMessage } = options;

    if (!task.reminder_enabled || !task.reminder_time) {
      return null;
    }

    try {
      const reminderTime = new Date(task.reminder_time);
      const now = new Date();

      if (reminderTime <= now) {
        console.warn('📱 Cannot schedule reminder for past time');
        return null;
      }

      const { title, body, icon } = this.getTaskReminderContent(task, reminderType, customMessage);

      const identifier = `task_reminder_${task.id}_${reminderType}`;

      await Notifications.scheduleNotificationAsync({
        identifier,
        content: {
          title,
          body,
          data: {
            taskId: task.id,
            reminderType,
            type: 'task_reminder'
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: reminderTime,
        },
      });

      console.log(`📱 Scheduled ${reminderType} reminder for task "${task.title}" at ${reminderTime.toLocaleString()}`);
      return identifier;
    } catch (error) {
      console.error('📱 Failed to schedule task reminder:', error);
      return null;
    }
  }

  async scheduleDeadlineReminder(task: Task, hoursBeforeDeadline: number = 24): Promise<string | null> {
    if (!task.due_date) return null;

    const reminderTime = new Date(task.due_date);
    reminderTime.setHours(reminderTime.getHours() - hoursBeforeDeadline);

    const now = new Date();
    if (reminderTime <= now) return null;

    try {
      const identifier = `deadline_reminder_${task.id}_${hoursBeforeDeadline}h`;

      await Notifications.scheduleNotificationAsync({
        identifier,
        content: {
          title: '⏰ Deadline Approaching',
          body: `"${task.title}" is due in ${hoursBeforeDeadline} hours`,
          data: {
            taskId: task.id,
            type: 'deadline_reminder',
            hoursBeforeDeadline
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: reminderTime,
        },
      });

      return identifier;
    } catch (error) {
      console.error('📱 Failed to schedule deadline reminder:', error);
      return null;
    }
  }

  async scheduleMilestoneReminder(task: Task): Promise<string | null> {
    if (!task.is_milestone || !task.due_date) return null;

    const reminderTime = new Date(task.due_date);
    reminderTime.setHours(reminderTime.getHours() - 48); // 2 days before

    const now = new Date();
    if (reminderTime <= now) return null;

    try {
      const identifier = `milestone_reminder_${task.id}`;

      await Notifications.scheduleNotificationAsync({
        identifier,
        content: {
          title: '🏆 Milestone Deadline Approaching',
          body: `Important milestone "${task.title}" is due in 2 days`,
          data: {
            taskId: task.id,
            type: 'milestone_reminder'
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
        },
        trigger: {
          date: reminderTime,
        },
      });

      return identifier;
    } catch (error) {
      console.error('📱 Failed to schedule milestone reminder:', error);
      return null;
    }
  }

  private getTaskReminderContent(task: Task, reminderType: string, customMessage?: string) {
    const priorityEmoji = {
      urgent: '🔥',
      high: '⚡',
      medium: '📋',
      low: '📝'
    };

    const emoji = priorityEmoji[task.priority] || '📋';

    switch (reminderType) {
      case 'due_soon':
        return {
          title: `${emoji} Task Due Soon`,
          body: customMessage || `"${task.title}" is due soon. Don't forget to complete it!`,
          icon: 'clock'
        };
      case 'overdue':
        return {
          title: '⚠️ Overdue Task',
          body: customMessage || `"${task.title}" is overdue. Please complete it as soon as possible.`,
          icon: 'alert'
        };
      case 'milestone_due':
        return {
          title: '🏆 Milestone Due',
          body: customMessage || `Important milestone "${task.title}" is due. Time to finish strong!`,
          icon: 'award'
        };
      default:
        return {
          title: `${emoji} Task Reminder`,
          body: customMessage || `Don't forget about "${task.title}"`,
          icon: 'bell'
        };
    }
  }

  async triggerHaptic(type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'medium'): Promise<void> {
    if (!this.isHapticsAvailable) return;

    try {
      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  // Achievement Notifications
  async showTaskCompletionNotification(task: Task): Promise<void> {
    const emoji = task.is_milestone ? '🏆' : '✅';
    const title = task.is_milestone ? 'Milestone Completed!' : 'Task Completed!';

    await this.showNotification({
      title: `${emoji} ${title}`,
      message: `Great job completing "${task.title}"!`,
      type: 'success',
      haptic: true,
    });
  }

  async showStreakAchievementNotification(streakCount: number, streakType: 'daily' | 'weekly'): Promise<void> {
    const emoji = streakCount >= 7 ? '🔥' : '⭐';
    const period = streakType === 'daily' ? 'days' : 'weeks';

    await this.showNotification({
      title: `${emoji} ${streakCount} ${period.charAt(0).toUpperCase() + period.slice(1)} Streak!`,
      message: `You've completed tasks for ${streakCount} ${period} in a row. Keep it up!`,
      type: 'success',
      haptic: true,
    });
  }

  async showProductivityMilestoneNotification(milestone: string): Promise<void> {
    await this.showNotification({
      title: '🎯 Productivity Milestone!',
      message: milestone,
      type: 'success',
      haptic: true,
    });
  }

  // Notification Management
  async cancelTaskReminders(taskId: string): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const taskNotifications = scheduledNotifications.filter(
        notification => notification.content.data?.taskId === taskId
      );

      for (const notification of taskNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }

      console.log(`📱 Cancelled ${taskNotifications.length} reminders for task ${taskId}`);
    } catch (error) {
      console.error('📱 Failed to cancel task reminders:', error);
    }
  }

  async cancelAllTaskReminders(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const taskNotifications = scheduledNotifications.filter(
        notification => notification.content.data?.type?.includes('reminder')
      );

      for (const notification of taskNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }

      console.log(`📱 Cancelled ${taskNotifications.length} task reminders`);
    } catch (error) {
      console.error('📱 Failed to cancel all task reminders:', error);
    }
  }

  async getScheduledTaskReminders(): Promise<Notifications.NotificationRequest[]> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      return scheduledNotifications.filter(
        notification => notification.content.data?.type?.includes('reminder')
      );
    } catch (error) {
      console.error('📱 Failed to get scheduled reminders:', error);
      return [];
    }
  }

  async rescheduleTaskReminders(task: Task): Promise<void> {
    // Cancel existing reminders
    await this.cancelTaskReminders(task.id);

    // Schedule new reminders if enabled
    if (task.reminder_enabled) {
      await this.scheduleTaskReminder({
        task,
        reminderType: 'custom'
      });

      // Schedule deadline reminders if due date exists
      if (task.due_date) {
        await this.scheduleDeadlineReminder(task, 24); // 24 hours before
        await this.scheduleDeadlineReminder(task, 2);  // 2 hours before

        // Schedule milestone reminder if it's a milestone
        if (task.is_milestone) {
          await this.scheduleMilestoneReminder(task);
        }
      }
    }
  }
}

export const notificationService = new NotificationService();
