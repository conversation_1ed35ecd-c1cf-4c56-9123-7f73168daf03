import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import * as BackgroundFetch from 'expo-background-fetch';

const BACKGROUND_TIMER_TASK = 'background-timer-task';
const TIMER_NOTIFICATION_ID = 'timer-notification';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

interface BackgroundTimerData {
  startTime: number;
  mode: 'stopwatch' | 'pomodoro';
  duration?: number; // For pomodoro mode
  subject?: string;
  taskName?: string;
  isRunning: boolean;
}

export class BackgroundTimerService {
  private static instance: BackgroundTimerService;
  private notificationId: string | null = null;

  static getInstance(): BackgroundTimerService {
    if (!BackgroundTimerService.instance) {
      BackgroundTimerService.instance = new BackgroundTimerService();
    }
    return BackgroundTimerService.instance;
  }

  async initialize() {
    try {
      // Request notification permissions
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
      }

      // Register background task
      await this.registerBackgroundTask();
    } catch (error) {
      console.error('Error initializing background timer service:', error);
    }
  }

  private async registerBackgroundTask() {
    try {
      // Define the background task
      TaskManager.defineTask(BACKGROUND_TIMER_TASK, async () => {
        try {
          const timerData = await this.getBackgroundTimerData();
          if (timerData && timerData.isRunning) {
            await this.updateTimerNotification(timerData);
          }
          return BackgroundFetch.BackgroundFetchResult.NewData;
        } catch (error) {
          console.error('Background task error:', error);
          return BackgroundFetch.BackgroundFetchResult.Failed;
        }
      });

      // Register the background fetch task
      await BackgroundFetch.registerTaskAsync(BACKGROUND_TIMER_TASK, {
        minimumInterval: 15000, // 15 seconds
        stopOnTerminate: false,
        startOnBoot: true,
      });
    } catch (error) {
      console.error('Error registering background task:', error);
    }
  }

  async startBackgroundTimer(data: BackgroundTimerData) {
    try {
      await AsyncStorage.setItem('background_timer_data', JSON.stringify(data));
      
      if (Platform.OS === 'ios') {
        // On iOS, show a persistent notification
        await this.showTimerNotification(data);
      }
    } catch (error) {
      console.error('Error starting background timer:', error);
    }
  }

  async stopBackgroundTimer() {
    try {
      await AsyncStorage.removeItem('background_timer_data');
      await this.cancelTimerNotification();
    } catch (error) {
      console.error('Error stopping background timer:', error);
    }
  }

  async pauseBackgroundTimer() {
    try {
      const timerData = await this.getBackgroundTimerData();
      if (timerData) {
        timerData.isRunning = false;
        await AsyncStorage.setItem('background_timer_data', JSON.stringify(timerData));
        await this.cancelTimerNotification();
      }
    } catch (error) {
      console.error('Error pausing background timer:', error);
    }
  }

  async resumeBackgroundTimer() {
    try {
      const timerData = await this.getBackgroundTimerData();
      if (timerData) {
        timerData.isRunning = true;
        timerData.startTime = Date.now(); // Reset start time for accurate calculation
        await AsyncStorage.setItem('background_timer_data', JSON.stringify(timerData));
        
        if (Platform.OS === 'ios') {
          await this.showTimerNotification(timerData);
        }
      }
    } catch (error) {
      console.error('Error resuming background timer:', error);
    }
  }

  async getBackgroundTimerData(): Promise<BackgroundTimerData | null> {
    try {
      const data = await AsyncStorage.getItem('background_timer_data');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting background timer data:', error);
      return null;
    }
  }

  async getElapsedTime(): Promise<number> {
    try {
      const timerData = await this.getBackgroundTimerData();
      if (!timerData || !timerData.isRunning) {
        return 0;
      }

      const elapsed = Math.floor((Date.now() - timerData.startTime) / 1000);
      return elapsed;
    } catch (error) {
      console.error('Error calculating elapsed time:', error);
      return 0;
    }
  }

  private async showTimerNotification(data: BackgroundTimerData) {
    try {
      const content = {
        title: 'Study Timer Running',
        body: this.getNotificationBody(data),
        data: { type: 'timer' },
        categoryIdentifier: 'timer',
        sound: false, // Don't play sound for ongoing timer
      };

      this.notificationId = await Notifications.scheduleNotificationAsync({
        content,
        trigger: null, // Show immediately
      });
    } catch (error) {
      console.error('Error showing timer notification:', error);
    }
  }

  private async updateTimerNotification(data: BackgroundTimerData) {
    try {
      if (this.notificationId) {
        await Notifications.dismissNotificationAsync(this.notificationId);
      }
      await this.showTimerNotification(data);
    } catch (error) {
      console.error('Error updating timer notification:', error);
    }
  }

  private async cancelTimerNotification() {
    try {
      if (this.notificationId) {
        await Notifications.dismissNotificationAsync(this.notificationId);
        this.notificationId = null;
      }
    } catch (error) {
      console.error('Error canceling timer notification:', error);
    }
  }

  private getNotificationBody(data: BackgroundTimerData): string {
    const elapsed = Math.floor((Date.now() - data.startTime) / 1000);
    const formattedTime = this.formatTime(elapsed);
    
    let body = `${formattedTime}`;
    
    if (data.mode === 'pomodoro' && data.duration) {
      const remaining = Math.max(0, data.duration - elapsed);
      body = `${this.formatTime(remaining)} remaining`;
    }
    
    if (data.subject) {
      body += ` • ${data.subject}`;
    }
    
    if (data.taskName) {
      body += ` • ${data.taskName}`;
    }
    
    return body;
  }

  private formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  // Notification actions for timer control
  async scheduleSessionCompleteNotification(sessionDuration: number, subject?: string) {
    try {
      const content = {
        title: 'Study Session Complete! 🎉',
        body: `You studied for ${this.formatTime(sessionDuration)}${subject ? ` on ${subject}` : ''}`,
        data: { type: 'session_complete' },
        sound: true,
      };

      await Notifications.scheduleNotificationAsync({
        content,
        trigger: null,
      });
    } catch (error) {
      console.error('Error scheduling session complete notification:', error);
    }
  }

  async schedulePomodoroBreakNotification(isLongBreak: boolean = false) {
    try {
      const content = {
        title: isLongBreak ? 'Long Break Time! ☕' : 'Break Time! ⏰',
        body: isLongBreak 
          ? 'Take a longer break - you\'ve earned it!' 
          : 'Take a short break and recharge',
        data: { type: 'break_time' },
        sound: true,
      };

      await Notifications.scheduleNotificationAsync({
        content,
        trigger: null,
      });
    } catch (error) {
      console.error('Error scheduling break notification:', error);
    }
  }

  async scheduleWorkTimeNotification() {
    try {
      const content = {
        title: 'Back to Work! 💪',
        body: 'Break time is over - let\'s get back to studying!',
        data: { type: 'work_time' },
        sound: true,
      };

      await Notifications.scheduleNotificationAsync({
        content,
        trigger: null,
      });
    } catch (error) {
      console.error('Error scheduling work notification:', error);
    }
  }

  // Clean up background tasks
  async cleanup() {
    try {
      await BackgroundFetch.unregisterTaskAsync(BACKGROUND_TIMER_TASK);
      await this.stopBackgroundTimer();
    } catch (error) {
      console.error('Error cleaning up background timer service:', error);
    }
  }
}

export const backgroundTimerService = BackgroundTimerService.getInstance();
