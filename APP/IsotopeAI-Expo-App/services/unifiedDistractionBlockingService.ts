import { Platform } from 'react-native';
import { iosDistractionBlockingService, IOSBlockedApp } from './iosDistractionBlockingService';
import { mobileAppMonitoringService, FocusContext, DistractionAttempt } from './mobileAppMonitoringService';

export interface UnifiedBlockedApp {
  id: string;
  name: string;
  packageName: string; // Android package name or iOS bundle identifier
  icon: string;
  category: string;
  isBlocked: boolean;
  blockDuringFocus: boolean;
  blockDuringBreaks: boolean;
}

export interface BlockingCapabilities {
  canBlockApps: boolean;
  requiresAuthorization: boolean;
  hasNativeUI: boolean;
  supportsScheduling: boolean;
  platform: 'ios' | 'android' | 'web';
}

class UnifiedDistractionBlockingService {
  private currentContext: FocusContext = 'normal';
  private isBlocking = false;
  private blockedApps: Set<string> = new Set();

  // Event listeners
  private onDistractionAttemptListeners: ((attempt: DistractionAttempt) => void)[] = [];
  private onBlockingStateChangeListeners: ((isBlocking: boolean) => void)[] = [];

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Listen to iOS distraction attempts
    if (Platform.OS === 'ios') {
      iosDistractionBlockingService.onDistractionAttempt((attempt) => {
        this.onDistractionAttemptListeners.forEach(listener => listener(attempt));
      });
    }

    // Listen to mobile app monitoring service (Android/fallback)
    mobileAppMonitoringService.onDistractionAttempt((attempt) => {
      this.onDistractionAttemptListeners.forEach(listener => listener(attempt));
    });
  }

  async getCapabilities(): Promise<BlockingCapabilities> {
    if (Platform.OS === 'ios') {
      const isAuthorized = await iosDistractionBlockingService.isAuthorizationGranted();
      return {
        canBlockApps: true,
        requiresAuthorization: true,
        hasNativeUI: true,
        supportsScheduling: true,
        platform: 'ios'
      };
    } else if (Platform.OS === 'android') {
      return {
        canBlockApps: false, // Limited on Android
        requiresAuthorization: false,
        hasNativeUI: false,
        supportsScheduling: false,
        platform: 'android'
      };
    } else {
      return {
        canBlockApps: false,
        requiresAuthorization: false,
        hasNativeUI: false,
        supportsScheduling: false,
        platform: 'web'
      };
    }
  }

  async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      return await iosDistractionBlockingService.requestAuthorization();
    }
    
    // Android and web don't require special permissions for our monitoring approach
    return true;
  }

  async isAuthorized(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      return await iosDistractionBlockingService.isAuthorizationGranted();
    }
    
    return true; // Android and web don't require authorization
  }

  async presentAppSelectionUI(): Promise<UnifiedBlockedApp[]> {
    if (Platform.OS === 'ios') {
      try {
        const iosApps = await iosDistractionBlockingService.presentAppSelectionUI();
        return iosApps.map((app, index) => ({
          id: `ios_${index}`,
          name: app.displayName,
          packageName: app.bundleIdentifier,
          icon: this.getIconForCategory(app.category),
          category: app.category,
          isBlocked: true,
          blockDuringFocus: true,
          blockDuringBreaks: false
        }));
      } catch (error) {
        console.error('Failed to present iOS app selection:', error);
        throw error;
      }
    }
    
    // For Android/web, return empty array since we can't present native UI
    return [];
  }

  async startBlocking(apps: UnifiedBlockedApp[], duration?: number): Promise<boolean> {
    const packageNames = apps.map(app => app.packageName);
    this.blockedApps = new Set(packageNames);
    
    if (Platform.OS === 'ios') {
      const success = await iosDistractionBlockingService.startBlocking(packageNames, duration);
      if (success) {
        this.isBlocking = true;
        this.notifyBlockingStateChange(true);
      }
      return success;
    } else {
      // Use mobile app monitoring service for Android/web
      mobileAppMonitoringService.updateBlockedApps(packageNames);
      mobileAppMonitoringService.setBlockingActive(true);
      mobileAppMonitoringService.startMonitoring(this.currentContext);
      
      this.isBlocking = true;
      this.notifyBlockingStateChange(true);
      return true;
    }
  }

  async stopBlocking(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      const success = await iosDistractionBlockingService.stopBlocking();
      if (success) {
        this.isBlocking = false;
        this.notifyBlockingStateChange(false);
      }
      return success;
    } else {
      // Stop mobile app monitoring service
      mobileAppMonitoringService.setBlockingActive(false);
      mobileAppMonitoringService.stopMonitoring();
      
      this.isBlocking = false;
      this.notifyBlockingStateChange(false);
      return true;
    }
  }

  async updateBlockedApps(apps: UnifiedBlockedApp[]): Promise<boolean> {
    const packageNames = apps.map(app => app.packageName);
    this.blockedApps = new Set(packageNames);
    
    if (Platform.OS === 'ios') {
      return await iosDistractionBlockingService.updateBlockedApps(packageNames);
    } else {
      mobileAppMonitoringService.updateBlockedApps(packageNames);
      return true;
    }
  }

  setContext(context: FocusContext) {
    this.currentContext = context;
    
    if (Platform.OS === 'ios') {
      iosDistractionBlockingService.setContext(context);
    } else {
      mobileAppMonitoringService.setContext(context);
    }
  }

  isAppBlocked(packageName: string): boolean {
    if (Platform.OS === 'ios') {
      return iosDistractionBlockingService.isAppBlocked(packageName);
    } else {
      return mobileAppMonitoringService.isAppBlocked(packageName);
    }
  }

  getBlockedApps(): string[] {
    if (Platform.OS === 'ios') {
      return iosDistractionBlockingService.getBlockedApps();
    } else {
      return Array.from(this.blockedApps);
    }
  }

  isCurrentlyBlocking(): boolean {
    if (Platform.OS === 'ios') {
      return iosDistractionBlockingService.isBlocking();
    } else {
      return this.isBlocking;
    }
  }

  getCommonApps(): UnifiedBlockedApp[] {
    if (Platform.OS === 'ios') {
      const iosApps = iosDistractionBlockingService.getCommonIOSApps();
      return iosApps.map((app, index) => ({
        id: `common_ios_${index}`,
        name: app.displayName,
        packageName: app.bundleIdentifier,
        icon: this.getIconForCategory(app.category),
        category: app.category,
        isBlocked: false,
        blockDuringFocus: true,
        blockDuringBreaks: false
      }));
    } else {
      // Return common Android apps
      return this.getCommonAndroidApps();
    }
  }

  private getCommonAndroidApps(): UnifiedBlockedApp[] {
    return [
      { id: 'android_1', name: 'Facebook', packageName: 'com.facebook.katana', icon: '📘', category: 'social', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_2', name: 'Instagram', packageName: 'com.instagram.android', icon: '📷', category: 'social', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_3', name: 'Twitter', packageName: 'com.twitter.android', icon: '🐦', category: 'social', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_4', name: 'TikTok', packageName: 'com.zhiliaoapp.musically', icon: '🎵', category: 'social', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_5', name: 'YouTube', packageName: 'com.google.android.youtube', icon: '📺', category: 'entertainment', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_6', name: 'Netflix', packageName: 'com.netflix.mediaclient', icon: '🎬', category: 'entertainment', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_7', name: 'Spotify', packageName: 'com.spotify.music', icon: '🎧', category: 'entertainment', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_8', name: 'Reddit', packageName: 'com.reddit.frontpage', icon: '🤖', category: 'social', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_9', name: 'Discord', packageName: 'com.discord', icon: '💬', category: 'social', isBlocked: false, blockDuringFocus: true, blockDuringBreaks: false },
      { id: 'android_10', name: 'Chrome', packageName: 'com.android.chrome', icon: '🌐', category: 'productivity', isBlocked: false, blockDuringFocus: false, blockDuringBreaks: false }
    ];
  }

  private getIconForCategory(category: string): string {
    const iconMap: { [key: string]: string } = {
      'social': '👥',
      'entertainment': '🎬',
      'games': '🎮',
      'news': '📰',
      'shopping': '🛒',
      'productivity': '💼',
      'other': '📱'
    };
    
    return iconMap[category] || '📱';
  }

  private notifyBlockingStateChange(isBlocking: boolean) {
    this.onBlockingStateChangeListeners.forEach(listener => listener(isBlocking));
  }

  // Event listeners
  onDistractionAttempt(listener: (attempt: DistractionAttempt) => void): () => void {
    this.onDistractionAttemptListeners.push(listener);
    return () => {
      const index = this.onDistractionAttemptListeners.indexOf(listener);
      if (index > -1) {
        this.onDistractionAttemptListeners.splice(index, 1);
      }
    };
  }

  onBlockingStateChange(listener: (isBlocking: boolean) => void): () => void {
    this.onBlockingStateChangeListeners.push(listener);
    return () => {
      const index = this.onBlockingStateChangeListeners.indexOf(listener);
      if (index > -1) {
        this.onBlockingStateChangeListeners.splice(index, 1);
      }
    };
  }
}

export const unifiedDistractionBlockingService = new UnifiedDistractionBlockingService();
