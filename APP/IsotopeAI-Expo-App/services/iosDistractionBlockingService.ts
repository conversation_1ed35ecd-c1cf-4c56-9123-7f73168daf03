import { Platform } from 'react-native';
import * as DeviceActivity from 'react-native-device-activity';
import { FocusContext, DistractionAttempt } from './mobileAppMonitoringService';

export interface IOSBlockedApp {
  bundleIdentifier: string;
  displayName: string;
  category: string;
}

export interface IOSBlockingSchedule {
  id: string;
  name: string;
  startTime: Date;
  endTime: Date;
  blockedApps: string[]; // Bundle identifiers
  isActive: boolean;
}

class IOSDistractionBlockingService {
  private isInitialized = false;
  private isAuthorized = false;
  private currentSchedule: IOSBlockingSchedule | null = null;
  private blockedApps: Set<string> = new Set();
  private currentContext: FocusContext = 'normal';
  
  // Event listeners
  private onDistractionAttemptListeners: ((attempt: DistractionAttempt) => void)[] = [];
  private onAuthorizationChangeListeners: ((authorized: boolean) => void)[] = [];

  constructor() {
    if (Platform.OS === 'ios') {
      this.initialize();
    }
  }

  private async initialize() {
    try {
      // Check if Family Controls is available
      const isSupported = await DeviceActivity.isSupported();
      if (!isSupported) {
        console.warn('📱 Family Controls not supported on this device');
        return;
      }

      // Check current authorization status
      this.isAuthorized = await DeviceActivity.isAuthorized();
      console.log(`📱 iOS Family Controls authorization status: ${this.isAuthorized}`);
      
      this.isInitialized = true;
    } catch (error) {
      console.error('📱 Failed to initialize iOS distraction blocking:', error);
    }
  }

  async requestAuthorization(): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.isInitialized) {
      return false;
    }

    try {
      this.isAuthorized = await DeviceActivity.requestAuthorization();
      console.log(`📱 iOS Family Controls authorization granted: ${this.isAuthorized}`);
      
      // Notify listeners
      this.onAuthorizationChangeListeners.forEach(listener => listener(this.isAuthorized));
      
      return this.isAuthorized;
    } catch (error) {
      console.error('📱 Failed to request iOS authorization:', error);
      return false;
    }
  }

  async isAuthorizationGranted(): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.isInitialized) {
      return false;
    }

    try {
      this.isAuthorized = await DeviceActivity.isAuthorized();
      return this.isAuthorized;
    } catch (error) {
      console.error('📱 Failed to check iOS authorization:', error);
      return false;
    }
  }

  async presentAppSelectionUI(): Promise<IOSBlockedApp[]> {
    if (Platform.OS !== 'ios' || !this.isAuthorized) {
      throw new Error('iOS Family Controls not authorized');
    }

    try {
      // Present the native app selection UI
      const selectedApps = await DeviceActivity.presentAppSelection();
      
      // Convert to our format
      const blockedApps: IOSBlockedApp[] = selectedApps.map((app: any) => ({
        bundleIdentifier: app.bundleIdentifier,
        displayName: app.displayName || app.bundleIdentifier,
        category: app.category || 'other'
      }));

      console.log(`📱 User selected ${blockedApps.length} apps to block:`, blockedApps);
      return blockedApps;
    } catch (error) {
      console.error('📱 Failed to present app selection UI:', error);
      throw error;
    }
  }

  async startBlocking(apps: string[], duration?: number): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.isAuthorized) {
      console.warn('📱 iOS Family Controls not authorized');
      return false;
    }

    try {
      const scheduleId = `focus_session_${Date.now()}`;
      const startTime = new Date();
      const endTime = duration ? new Date(Date.now() + duration * 1000) : new Date(Date.now() + 24 * 60 * 60 * 1000); // Default 24 hours

      // Create blocking schedule
      const schedule: IOSBlockingSchedule = {
        id: scheduleId,
        name: 'Focus Session',
        startTime,
        endTime,
        blockedApps: apps,
        isActive: true
      };

      // Start the device activity monitoring
      await DeviceActivity.startMonitoring({
        scheduleId,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        blockedApplications: apps,
        warningTime: 60, // 1 minute warning before blocking
        shieldConfiguration: {
          title: '🎯 Focus Mode Active',
          subtitle: 'This app is blocked during your focus session',
          primaryButtonLabel: 'OK',
          secondaryButtonLabel: 'Ask for More Time'
        }
      });

      this.currentSchedule = schedule;
      this.blockedApps = new Set(apps);
      
      console.log(`📱 Started iOS app blocking for ${apps.length} apps until ${endTime.toLocaleTimeString()}`);
      return true;
    } catch (error) {
      console.error('📱 Failed to start iOS app blocking:', error);
      return false;
    }
  }

  async stopBlocking(): Promise<boolean> {
    if (Platform.OS !== 'ios' || !this.currentSchedule) {
      return false;
    }

    try {
      // Stop the device activity monitoring
      await DeviceActivity.stopMonitoring(this.currentSchedule.id);
      
      console.log(`📱 Stopped iOS app blocking for schedule: ${this.currentSchedule.id}`);
      
      this.currentSchedule = null;
      this.blockedApps.clear();
      
      return true;
    } catch (error) {
      console.error('📱 Failed to stop iOS app blocking:', error);
      return false;
    }
  }

  async updateBlockedApps(apps: string[]): Promise<boolean> {
    if (this.currentSchedule) {
      // Stop current blocking and start with new apps
      await this.stopBlocking();
      return await this.startBlocking(apps);
    }
    
    this.blockedApps = new Set(apps);
    return true;
  }

  setContext(context: FocusContext) {
    this.currentContext = context;
  }

  isAppBlocked(bundleIdentifier: string): boolean {
    if (this.currentContext === 'normal') return false;
    if (this.currentContext === 'break') return false; // Don't block during breaks
    
    return this.blockedApps.has(bundleIdentifier);
  }

  getCurrentSchedule(): IOSBlockingSchedule | null {
    return this.currentSchedule;
  }

  getBlockedApps(): string[] {
    return Array.from(this.blockedApps);
  }

  isBlocking(): boolean {
    return this.currentSchedule?.isActive || false;
  }

  // Event listeners
  onDistractionAttempt(listener: (attempt: DistractionAttempt) => void): () => void {
    this.onDistractionAttemptListeners.push(listener);
    return () => {
      const index = this.onDistractionAttemptListeners.indexOf(listener);
      if (index > -1) {
        this.onDistractionAttemptListeners.splice(index, 1);
      }
    };
  }

  onAuthorizationChange(listener: (authorized: boolean) => void): () => void {
    this.onAuthorizationChangeListeners.push(listener);
    return () => {
      const index = this.onAuthorizationChangeListeners.indexOf(listener);
      if (index > -1) {
        this.onAuthorizationChangeListeners.splice(index, 1);
      }
    };
  }

  private handleDistractionAttempt(appName: string, bundleIdentifier: string, blocked: boolean) {
    const attempt: DistractionAttempt = {
      appName,
      packageName: bundleIdentifier,
      timestamp: new Date(),
      blocked,
      context: this.currentContext,
      duration: 0
    };

    this.onDistractionAttemptListeners.forEach(listener => listener(attempt));
  }

  // Get common iOS apps for blocking suggestions
  getCommonIOSApps(): IOSBlockedApp[] {
    return [
      { bundleIdentifier: 'com.facebook.Facebook', displayName: 'Facebook', category: 'social' },
      { bundleIdentifier: 'com.atebits.Tweetie2', displayName: 'Twitter', category: 'social' },
      { bundleIdentifier: 'com.burbn.instagram', displayName: 'Instagram', category: 'social' },
      { bundleIdentifier: 'com.zhiliaoapp.musically', displayName: 'TikTok', category: 'social' },
      { bundleIdentifier: 'com.snapchat.snapchat', displayName: 'Snapchat', category: 'social' },
      { bundleIdentifier: 'com.linkedin.LinkedIn', displayName: 'LinkedIn', category: 'social' },
      { bundleIdentifier: 'com.reddit.Reddit', displayName: 'Reddit', category: 'social' },
      { bundleIdentifier: 'com.discord', displayName: 'Discord', category: 'social' },
      { bundleIdentifier: 'com.google.ios.youtube', displayName: 'YouTube', category: 'entertainment' },
      { bundleIdentifier: 'com.netflix.Netflix', displayName: 'Netflix', category: 'entertainment' },
      { bundleIdentifier: 'com.spotify.client', displayName: 'Spotify', category: 'entertainment' },
      { bundleIdentifier: 'com.apple.news', displayName: 'Apple News', category: 'news' },
      { bundleIdentifier: 'com.amazon.Amazon', displayName: 'Amazon', category: 'shopping' },
      { bundleIdentifier: 'com.apple.mobilesafari', displayName: 'Safari', category: 'productivity' },
      { bundleIdentifier: 'com.google.chrome.ios', displayName: 'Chrome', category: 'productivity' }
    ];
  }
}

export const iosDistractionBlockingService = new IOSDistractionBlockingService();
