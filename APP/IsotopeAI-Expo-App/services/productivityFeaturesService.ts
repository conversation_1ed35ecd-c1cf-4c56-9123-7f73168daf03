import { enhancedSupabaseService } from './enhancedSupabaseService';
import { storageService } from './storageService';
import { StreakInfo, UserProfile } from '@/types/app';

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'streak' | 'time' | 'session' | 'subject' | 'milestone';
  requirement: number;
  unlocked: boolean;
  unlockedAt?: Date;
  progress: number;
  maxProgress: number;
}

export interface MotivationalMessage {
  id: string;
  message: string;
  category: 'morning' | 'afternoon' | 'evening' | 'streak' | 'achievement' | 'encouragement';
  condition?: string;
}

export interface DailyTarget {
  id: string;
  userId: string;
  targetMinutes: number;
  currentMinutes: number;
  date: string;
  achieved: boolean;
  streakCount: number;
}

class ProductivityFeaturesService {
  private readonly STORAGE_KEYS = {
    ACHIEVEMENTS: 'isotope_achievements',
    DAILY_TARGETS: 'isotope_daily_targets',
    MOTIVATIONAL_MESSAGES: 'isotope_motivational_messages',
    USER_PREFERENCES: 'isotope_productivity_preferences',
  };

  // Achievement definitions
  private readonly ACHIEVEMENT_DEFINITIONS: Omit<Achievement, 'unlocked' | 'unlockedAt' | 'progress'>[] = [
    {
      id: 'first_session',
      title: 'Getting Started',
      description: 'Complete your first study session',
      icon: '🎯',
      category: 'session',
      requirement: 1,
      maxProgress: 1,
    },
    {
      id: 'streak_3',
      title: 'Consistency Builder',
      description: 'Study for 3 consecutive days',
      icon: '🔥',
      category: 'streak',
      requirement: 3,
      maxProgress: 3,
    },
    {
      id: 'streak_7',
      title: 'Week Warrior',
      description: 'Study for 7 consecutive days',
      icon: '⚡',
      category: 'streak',
      requirement: 7,
      maxProgress: 7,
    },
    {
      id: 'streak_30',
      title: 'Month Master',
      description: 'Study for 30 consecutive days',
      icon: '👑',
      category: 'streak',
      requirement: 30,
      maxProgress: 30,
    },
    {
      id: 'time_10h',
      title: 'Dedicated Learner',
      description: 'Study for 10 total hours',
      icon: '📚',
      category: 'time',
      requirement: 600, // 10 hours in minutes
      maxProgress: 600,
    },
    {
      id: 'time_50h',
      title: 'Study Champion',
      description: 'Study for 50 total hours',
      icon: '🏆',
      category: 'time',
      requirement: 3000, // 50 hours in minutes
      maxProgress: 3000,
    },
    {
      id: 'time_100h',
      title: 'Knowledge Seeker',
      description: 'Study for 100 total hours',
      icon: '🌟',
      category: 'time',
      requirement: 6000, // 100 hours in minutes
      maxProgress: 6000,
    },
    {
      id: 'session_25',
      title: 'Session Specialist',
      description: 'Complete 25 study sessions',
      icon: '🎓',
      category: 'session',
      requirement: 25,
      maxProgress: 25,
    },
    {
      id: 'subject_master',
      title: 'Subject Master',
      description: 'Study 5 different subjects',
      icon: '🧠',
      category: 'subject',
      requirement: 5,
      maxProgress: 5,
    },
    {
      id: 'daily_target_7',
      title: 'Target Achiever',
      description: 'Meet daily target for 7 days',
      icon: '🎯',
      category: 'milestone',
      requirement: 7,
      maxProgress: 7,
    },
  ];

  // Motivational messages
  private readonly MOTIVATIONAL_MESSAGES: MotivationalMessage[] = [
    {
      id: 'morning_1',
      message: 'Good morning! Ready to make today productive? 🌅',
      category: 'morning',
    },
    {
      id: 'morning_2',
      message: 'A new day, a new opportunity to learn! ☀️',
      category: 'morning',
    },
    {
      id: 'afternoon_1',
      message: 'Keep the momentum going! You\'re doing great! 💪',
      category: 'afternoon',
    },
    {
      id: 'evening_1',
      message: 'Reflect on today\'s progress. Every step counts! 🌙',
      category: 'evening',
    },
    {
      id: 'streak_3',
      message: 'Amazing! You\'re on a 3-day streak! Keep it up! 🔥',
      category: 'streak',
      condition: 'streak >= 3',
    },
    {
      id: 'streak_7',
      message: 'Incredible! A full week of consistent studying! ⚡',
      category: 'streak',
      condition: 'streak >= 7',
    },
    {
      id: 'encouragement_1',
      message: 'Progress, not perfection. You\'re on the right path! 🌱',
      category: 'encouragement',
    },
    {
      id: 'encouragement_2',
      message: 'Small steps lead to big achievements! 🚀',
      category: 'encouragement',
    },
  ];

  async initializeUserAchievements(userId: string): Promise<Achievement[]> {
    try {
      const stored = await storageService.getObject<Achievement[]>(
        `${this.STORAGE_KEYS.ACHIEVEMENTS}_${userId}`
      );

      if (stored) {
        return stored;
      }

      // Initialize with default achievements
      const initialAchievements: Achievement[] = this.ACHIEVEMENT_DEFINITIONS.map(def => ({
        ...def,
        unlocked: false,
        progress: 0,
      }));

      await storageService.setObject(
        `${this.STORAGE_KEYS.ACHIEVEMENTS}_${userId}`,
        initialAchievements
      );

      return initialAchievements;
    } catch (error) {
      console.error('Error initializing achievements:', error);
      return [];
    }
  }

  async updateAchievementProgress(
    userId: string,
    achievementId: string,
    progress: number
  ): Promise<Achievement | null> {
    try {
      const achievements = await this.initializeUserAchievements(userId);
      const achievement = achievements.find(a => a.id === achievementId);

      if (!achievement) return null;

      achievement.progress = Math.min(progress, achievement.maxProgress);

      if (!achievement.unlocked && achievement.progress >= achievement.requirement) {
        achievement.unlocked = true;
        achievement.unlockedAt = new Date();
      }

      await storageService.setObject(
        `${this.STORAGE_KEYS.ACHIEVEMENTS}_${userId}`,
        achievements
      );

      return achievement;
    } catch (error) {
      console.error('Error updating achievement progress:', error);
      return null;
    }
  }

  async checkAndUpdateAchievements(userId: string): Promise<Achievement[]> {
    try {
      // Get user data for achievement calculations
      const [sessionsResponse, streakResponse] = await Promise.all([
        enhancedSupabaseService.getStudySessions(userId, {
          start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // Last year
          end: new Date(),
        }),
        enhancedSupabaseService.getStreakInfo(userId),
      ]);

      if (!sessionsResponse.success || !streakResponse.success) {
        return [];
      }

      const sessions = sessionsResponse.data || [];
      const streakInfo = streakResponse.data;

      const achievements = await this.initializeUserAchievements(userId);
      const newlyUnlocked: Achievement[] = [];

      // Calculate metrics
      const totalSessions = sessions.length;
      const totalTimeMinutes = sessions.reduce((sum, s) => sum + Math.round(s.duration / 60), 0);
      const uniqueSubjects = new Set(sessions.map(s => s.subject)).size;
      const currentStreak = streakInfo?.currentStreak || 0;

      // Update achievements
      for (const achievement of achievements) {
        let newProgress = achievement.progress;

        switch (achievement.category) {
          case 'session':
            newProgress = totalSessions;
            break;
          case 'time':
            newProgress = totalTimeMinutes;
            break;
          case 'subject':
            newProgress = uniqueSubjects;
            break;
          case 'streak':
            newProgress = currentStreak;
            break;
        }

        if (newProgress > achievement.progress) {
          const updated = await this.updateAchievementProgress(userId, achievement.id, newProgress);
          if (updated && updated.unlocked && !achievement.unlocked) {
            newlyUnlocked.push(updated);
          }
        }
      }

      return newlyUnlocked;
    } catch (error) {
      console.error('Error checking achievements:', error);
      return [];
    }
  }

  async getDailyTarget(userId: string, date?: string): Promise<DailyTarget | null> {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0];
      const targets = await storageService.getObject<DailyTarget[]>(
        `${this.STORAGE_KEYS.DAILY_TARGETS}_${userId}`
      ) || [];

      return targets.find(t => t.date === targetDate) || null;
    } catch (error) {
      console.error('Error getting daily target:', error);
      return null;
    }
  }

  async setDailyTarget(userId: string, targetMinutes: number, date?: string): Promise<DailyTarget> {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0];
      const targets = await storageService.getObject<DailyTarget[]>(
        `${this.STORAGE_KEYS.DAILY_TARGETS}_${userId}`
      ) || [];

      const existingIndex = targets.findIndex(t => t.date === targetDate);
      const target: DailyTarget = {
        id: existingIndex >= 0 ? targets[existingIndex].id : Date.now().toString(),
        userId,
        targetMinutes,
        currentMinutes: existingIndex >= 0 ? targets[existingIndex].currentMinutes : 0,
        date: targetDate,
        achieved: false,
        streakCount: 0,
      };

      target.achieved = target.currentMinutes >= target.targetMinutes;

      if (existingIndex >= 0) {
        targets[existingIndex] = target;
      } else {
        targets.push(target);
      }

      await storageService.setObject(`${this.STORAGE_KEYS.DAILY_TARGETS}_${userId}`, targets);
      return target;
    } catch (error) {
      console.error('Error setting daily target:', error);
      throw error;
    }
  }

  getMotivationalMessage(category: 'morning' | 'afternoon' | 'evening' | 'streak' | 'achievement' | 'encouragement', context?: any): MotivationalMessage {
    const messages = this.MOTIVATIONAL_MESSAGES.filter(m => m.category === category);
    
    if (messages.length === 0) {
      return {
        id: 'default',
        message: 'Keep up the great work! 💪',
        category: 'encouragement',
      };
    }

    // For streak messages, find appropriate one based on streak count
    if (category === 'streak' && context?.streakCount) {
      const streakMessages = messages.filter(m => {
        if (!m.condition) return false;
        const match = m.condition.match(/streak >= (\d+)/);
        return match && parseInt(match[1]) <= context.streakCount;
      });
      
      if (streakMessages.length > 0) {
        return streakMessages[streakMessages.length - 1]; // Get the highest applicable streak message
      }
    }

    // Return random message from category
    return messages[Math.floor(Math.random() * messages.length)];
  }

  async getUserAchievements(userId: string): Promise<Achievement[]> {
    return this.initializeUserAchievements(userId);
  }

  getAchievementsByCategory(achievements: Achievement[], category: Achievement['category']): Achievement[] {
    return achievements.filter(a => a.category === category);
  }

  getUnlockedAchievements(achievements: Achievement[]): Achievement[] {
    return achievements.filter(a => a.unlocked);
  }

  getProgressPercentage(achievement: Achievement): number {
    return Math.min((achievement.progress / achievement.maxProgress) * 100, 100);
  }
}

export const productivityFeaturesService = new ProductivityFeaturesService();
