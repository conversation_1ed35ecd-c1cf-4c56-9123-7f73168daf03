import { AppState, AppStateStatus, Platform, Linking, Alert } from 'react-native';
import { notificationService } from './notificationService';
import * as Device from 'expo-device';
import * as Application from 'expo-application';
import * as Haptics from 'expo-haptics';
import Constants from 'expo-constants';

export type FocusContext = 'focus' | 'break' | 'normal';

export interface AppSwitchEvent {
  timestamp: Date;
  fromApp: string;
  toApp: string;
  context: FocusContext;
  duration: number; // Time spent in the previous app
}

export interface DistractionAttempt {
  timestamp: Date;
  appName: string;
  packageName: string;
  context: FocusContext;
  blocked: boolean;
  duration: number; // How long they tried to use the app
}

class MobileAppMonitoringService {
  private isMonitoring = false;
  private currentContext: FocusContext = 'normal';
  private appStateSubscription: any = null;
  private currentAppState: AppStateStatus = 'active';
  private lastAppSwitchTime = Date.now();
  private focusAppPackage = 'com.isotope.ai.timemanagement'; // Our app's package name
  private blockedApps: Set<string> = new Set();
  private strictMode = false;
  private blockingActive = false;
  private appSwitchCheckInterval: NodeJS.Timeout | null = null;
  private lastForegroundTime = Date.now();
  private consecutiveBackgroundTime = 0;

  // Event listeners
  private onDistractionAttemptListeners: ((attempt: DistractionAttempt) => void)[] = [];
  private onAppSwitchListeners: ((event: AppSwitchEvent) => void)[] = [];

  constructor() {
    this.setupAppStateListener();
  }

  private setupAppStateListener() {
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    const now = Date.now();
    const duration = now - this.lastAppSwitchTime;

    if (this.isMonitoring && this.blockingActive) {
      if (this.currentAppState === 'active' && nextAppState === 'background') {
        // User switched away from our app - start monitoring for blocked apps
        this.lastForegroundTime = now;
        this.startAppSwitchMonitoring();
        this.handleAppSwitch('focus_app', 'unknown_app', duration);
      } else if (this.currentAppState === 'background' && nextAppState === 'active') {
        // User returned to our app
        this.stopAppSwitchMonitoring();
        this.handleAppReturn(duration);
      }
    }

    this.currentAppState = nextAppState;
    this.lastAppSwitchTime = now;
  };

  private handleAppSwitch(fromApp: string, toApp: string, duration: number) {
    const event: AppSwitchEvent = {
      timestamp: new Date(),
      fromApp,
      toApp,
      context: this.currentContext,
      duration,
    };

    // Notify listeners
    this.onAppSwitchListeners.forEach(listener => listener(event));

    // If switching during focus time, record as potential distraction
    if (this.currentContext === 'focus') {
      this.recordPotentialDistraction(toApp, duration);
    }
  }

  private handleAppReturn(awayDuration: number) {
    if (this.currentContext === 'focus' && awayDuration > 5000) { // More than 5 seconds away
      // Show encouragement message
      notificationService.showFocusEncouragement();
    }
  }

  private recordPotentialDistraction(appName: string, duration: number) {
    // Simulate checking if the app is blocked
    const isBlocked = this.isAppBlocked(appName);
    
    const attempt: DistractionAttempt = {
      timestamp: new Date(),
      appName: appName === 'unknown_app' ? 'Unknown App' : appName,
      packageName: appName,
      context: this.currentContext,
      blocked: isBlocked,
      duration,
    };

    // Notify listeners
    this.onDistractionAttemptListeners.forEach(listener => listener(attempt));

    // Show blocking notification if app is blocked
    if (isBlocked) {
      notificationService.showDistractionBlocked(attempt.appName);
    }
  }

  private isAppBlocked(packageName: string): boolean {
    if (this.currentContext === 'normal') return false;
    if (this.currentContext === 'break' && !this.strictMode) return false;
    
    return this.blockedApps.has(packageName) || packageName === 'unknown_app';
  }

  // Public methods
  startMonitoring(context: FocusContext = 'focus') {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.currentContext = context;
    this.lastAppSwitchTime = Date.now();
    this.lastForegroundTime = Date.now();
    this.currentAppState = AppState.currentState;

    // Subscribe to app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);

    console.log(`📱 Started app monitoring in ${context} context`);
  }

  stopMonitoring() {
    this.isMonitoring = false;
    this.currentContext = 'normal';
    this.blockingActive = false;

    // Clean up subscriptions and intervals
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    this.stopAppSwitchMonitoring();

    console.log('📱 Stopped app monitoring');
  }

  setContext(context: FocusContext) {
    this.currentContext = context;
    
    if (context === 'normal') {
      this.stopMonitoring();
    } else if (!this.isMonitoring) {
      this.startMonitoring(context);
    }
  }

  updateBlockedApps(apps: string[]) {
    this.blockedApps = new Set(apps);
  }

  setStrictMode(enabled: boolean) {
    this.strictMode = enabled;
  }

  // Get suggested apps to block based on platform
  getSuggestedBlockedApps() {
    return this.getCommonBlockedApps().slice(0, 10); // Return top 10 most common
  }

  setBlockingActive(active: boolean) {
    this.blockingActive = active;
    if (!active) {
      this.stopAppSwitchMonitoring();
    }
  }

  private startAppSwitchMonitoring() {
    if (this.appSwitchCheckInterval) {
      clearInterval(this.appSwitchCheckInterval);
    }

    // Check every 1000ms for enhanced detection
    this.appSwitchCheckInterval = setInterval(() => {
      this.enhancedBlockingDetection();
    }, 1000);
  }

  private stopAppSwitchMonitoring() {
    if (this.appSwitchCheckInterval) {
      clearInterval(this.appSwitchCheckInterval);
      this.appSwitchCheckInterval = null;
    }
    this.consecutiveBackgroundTime = 0;
  }

  private async handlePotentialBlockedAppUsage() {
    if (!this.blockingActive || this.blockedApps.size === 0) {
      return;
    }

    // Since we can't directly detect which app is being used on mobile,
    // we'll show a blocking notification and try to bring our app back to foreground
    await this.showBlockingNotification();

    // Trigger haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } else {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }

    // Try to bring our app back to foreground
    this.attemptToReturnToFocusApp();
  }

  private async showBlockingNotification() {
    const title = this.strictMode ? 'Focus Mode Active' : 'Distraction Detected';
    const body = this.strictMode
      ? 'You are in strict focus mode. Return to your timer to continue.'
      : 'Distraction detected. Tap to return to your focus session.';

    await notificationService.scheduleNotification({
      title,
      body,
      data: { type: 'blocking_reminder' },
    });
  }

  private attemptToReturnToFocusApp() {
    // On mobile, we can't force bring our app to foreground,
    // but we can show an alert when the user returns
    if (this.strictMode) {
      // In strict mode, we'll show a more persistent reminder
      setTimeout(() => {
        if (this.currentAppState === 'background' && this.blockingActive) {
          Alert.alert(
            'Focus Mode Active',
            'You are in strict focus mode. Please return to your timer to continue your session.',
            [{ text: 'OK', style: 'default' }]
          );
        }
      }, 1000);
    }
  }

  // Enhanced app detection methods
  async getDeviceInfo() {
    return {
      deviceType: Device.deviceType,
      deviceName: Device.deviceName,
      osName: Device.osName,
      osVersion: Device.osVersion,
      platform: Platform.OS,
      appVersion: Application.nativeApplicationVersion,
      buildVersion: Application.nativeBuildVersion,
      bundleId: Application.applicationId,
    };
  }

  // Get list of commonly blocked apps with their package names
  getCommonBlockedApps() {
    const commonApps = {
      ios: [
        { name: 'Instagram', packageName: 'com.burbn.instagram', category: 'social' },
        { name: 'Facebook', packageName: 'com.facebook.Facebook', category: 'social' },
        { name: 'Twitter/X', packageName: 'com.atebits.Tweetie2', category: 'social' },
        { name: 'TikTok', packageName: 'com.zhiliaoapp.musically', category: 'social' },
        { name: 'YouTube', packageName: 'com.google.ios.youtube', category: 'entertainment' },
        { name: 'Netflix', packageName: 'com.netflix.Netflix', category: 'entertainment' },
        { name: 'Snapchat', packageName: 'com.toyopagroup.picaboo', category: 'social' },
        { name: 'WhatsApp', packageName: 'net.whatsapp.WhatsApp', category: 'messaging' },
        { name: 'Telegram', packageName: 'ph.telegra.Telegraph', category: 'messaging' },
        { name: 'Reddit', packageName: 'com.reddit.Reddit', category: 'social' },
        { name: 'Discord', packageName: 'com.hammerandchisel.discord', category: 'social' },
        { name: 'Twitch', packageName: 'tv.twitch', category: 'entertainment' },
        { name: 'Spotify', packageName: 'com.spotify.client', category: 'music' },
        { name: 'Safari', packageName: 'com.apple.mobilesafari', category: 'browser' },
        { name: 'Chrome', packageName: 'com.google.chrome.ios', category: 'browser' },
      ],
      android: [
        { name: 'Instagram', packageName: 'com.instagram.android', category: 'social' },
        { name: 'Facebook', packageName: 'com.facebook.katana', category: 'social' },
        { name: 'Twitter/X', packageName: 'com.twitter.android', category: 'social' },
        { name: 'TikTok', packageName: 'com.zhiliaoapp.musically', category: 'social' },
        { name: 'YouTube', packageName: 'com.google.android.youtube', category: 'entertainment' },
        { name: 'Netflix', packageName: 'com.netflix.mediaclient', category: 'entertainment' },
        { name: 'Snapchat', packageName: 'com.snapchat.android', category: 'social' },
        { name: 'WhatsApp', packageName: 'com.whatsapp', category: 'messaging' },
        { name: 'Telegram', packageName: 'org.telegram.messenger', category: 'messaging' },
        { name: 'Reddit', packageName: 'com.reddit.frontpage', category: 'social' },
        { name: 'Discord', packageName: 'com.discord', category: 'social' },
        { name: 'Twitch', packageName: 'tv.twitch.android.app', category: 'entertainment' },
        { name: 'Spotify', packageName: 'com.spotify.music', category: 'music' },
        { name: 'Chrome', packageName: 'com.android.chrome', category: 'browser' },
        { name: 'Firefox', packageName: 'org.mozilla.firefox', category: 'browser' },
      ]
    };

    return Platform.OS === 'ios' ? commonApps.ios : commonApps.android;
  }

  // Enhanced blocking detection with better timing
  private enhancedBlockingDetection() {
    if (!this.blockingActive || this.currentAppState !== 'background') {
      return;
    }

    const timeInBackground = Date.now() - this.lastForegroundTime;

    // If user has been away for more than 3 seconds, likely using another app
    if (timeInBackground > 3000) {
      this.handleSuspectedAppUsage(timeInBackground);
    }
  }

  private async handleSuspectedAppUsage(timeAway: number) {
    // Record the distraction attempt
    const suspectedApp = this.guessMostLikelyBlockedApp();

    if (suspectedApp) {
      this.handleDistractionAttempt(suspectedApp.name, suspectedApp.packageName, true);
    }

    // Show blocking notification
    await this.showEnhancedBlockingNotification(timeAway);

    // Trigger haptic feedback
    this.triggerBlockingHaptics();
  }

  private guessMostLikelyBlockedApp() {
    // Simple heuristic: return the first blocked social media app
    const commonApps = this.getCommonBlockedApps();
    const socialApps = commonApps.filter(app =>
      app.category === 'social' && this.blockedApps.has(app.packageName)
    );

    return socialApps.length > 0 ? socialApps[0] : null;
  }

  private async showEnhancedBlockingNotification(timeAway: number) {
    if (this.strictMode) {
      await notificationService.showStrictModeBlocking(timeAway);
    } else {
      await notificationService.showDistractionDetected(timeAway);
    }
  }

  private triggerBlockingHaptics() {
    if (Platform.OS === 'ios') {
      // Use different haptic patterns based on strict mode
      if (this.strictMode) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      } else {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      }
    } else {
      // Android haptic feedback
      if (this.strictMode) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
      } else {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    }
  }

  // Simulation methods for testing
  simulateAppSwitch(appName: string, packageName: string) {
    if (!this.isMonitoring) return;

    const duration = Math.random() * 10000 + 1000; // Random duration between 1-11 seconds
    this.handleAppSwitch('focus_app', packageName, duration);
    
    // Simulate returning to focus app after a short time
    setTimeout(() => {
      this.handleAppReturn(duration);
    }, duration);
  }

  simulateDistractionAttempt(appName: string, packageName: string) {
    if (!this.isMonitoring) return;

    const duration = Math.random() * 5000 + 500; // Random duration between 0.5-5.5 seconds
    this.recordPotentialDistraction(packageName, duration);
  }

  // Event listeners
  onDistractionAttempt(listener: (attempt: DistractionAttempt) => void) {
    this.onDistractionAttemptListeners.push(listener);
    
    return () => {
      const index = this.onDistractionAttemptListeners.indexOf(listener);
      if (index > -1) {
        this.onDistractionAttemptListeners.splice(index, 1);
      }
    };
  }

  onAppSwitch(listener: (event: AppSwitchEvent) => void) {
    this.onAppSwitchListeners.push(listener);
    
    return () => {
      const index = this.onAppSwitchListeners.indexOf(listener);
      if (index > -1) {
        this.onAppSwitchListeners.splice(index, 1);
      }
    };
  }

  // Cleanup
  destroy() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    this.onDistractionAttemptListeners = [];
    this.onAppSwitchListeners = [];
  }

  // Getters
  get isActive() {
    return this.isMonitoring;
  }

  get context() {
    return this.currentContext;
  }
}

export const mobileAppMonitoringService = new MobileAppMonitoringService();
