import { Platform } from 'react-native';

export interface InstalledApp {
  appName: string;
  packageName: string;
  icon: string;
  category?: string;
}

export type AppCategory = 'social' | 'entertainment' | 'games' | 'productivity' | 'news' | 'shopping' | 'other';

class InstalledAppsService {
  private commonApps: InstalledApp[] = [
    // Social Media
    { appName: 'Instagram', packageName: 'com.instagram.android', icon: '📷', category: 'social' },
    { appName: 'Facebook', packageName: 'com.facebook.katana', icon: '📘', category: 'social' },
    { appName: 'Twitter', packageName: 'com.twitter.android', icon: '🐦', category: 'social' },
    { appName: 'TikTok', packageName: 'com.zhiliaoapp.musically', icon: '🎵', category: 'social' },
    { appName: 'Snapchat', packageName: 'com.snapchat.android', icon: '👻', category: 'social' },
    { appName: 'WhatsApp', packageName: 'com.whatsapp', icon: '💬', category: 'social' },
    { appName: 'Telegram', packageName: 'org.telegram.messenger', icon: '✈️', category: 'social' },
    { appName: 'Discord', packageName: 'com.discord', icon: '🎮', category: 'social' },
    { appName: 'LinkedIn', packageName: 'com.linkedin.android', icon: '💼', category: 'social' },
    { appName: 'Reddit', packageName: 'com.reddit.frontpage', icon: '🤖', category: 'social' },

    // Entertainment
    { appName: 'YouTube', packageName: 'com.google.android.youtube', icon: '📺', category: 'entertainment' },
    { appName: 'Netflix', packageName: 'com.netflix.mediaclient', icon: '🎬', category: 'entertainment' },
    { appName: 'Spotify', packageName: 'com.spotify.music', icon: '🎧', category: 'entertainment' },
    { appName: 'Amazon Prime Video', packageName: 'com.amazon.avod.thirdpartyclient', icon: '📽️', category: 'entertainment' },
    { appName: 'Disney+', packageName: 'com.disney.disneyplus', icon: '🏰', category: 'entertainment' },
    { appName: 'Hulu', packageName: 'com.hulu.plus', icon: '📱', category: 'entertainment' },
    { appName: 'Twitch', packageName: 'tv.twitch.android.app', icon: '🎮', category: 'entertainment' },
    { appName: 'Apple Music', packageName: 'com.apple.android.music', icon: '🎵', category: 'entertainment' },

    // Games
    { appName: 'Candy Crush Saga', packageName: 'com.king.candycrushsaga', icon: '🍭', category: 'games' },
    { appName: 'PUBG Mobile', packageName: 'com.tencent.ig', icon: '🔫', category: 'games' },
    { appName: 'Among Us', packageName: 'com.innersloth.spacemafia', icon: '👾', category: 'games' },
    { appName: 'Clash of Clans', packageName: 'com.supercell.clashofclans', icon: '⚔️', category: 'games' },
    { appName: 'Minecraft', packageName: 'com.mojang.minecraftpe', icon: '🧱', category: 'games' },
    { appName: 'Fortnite', packageName: 'com.epicgames.fortnite', icon: '🏗️', category: 'games' },

    // News
    { appName: 'CNN', packageName: 'com.cnn.mobile.android.phone', icon: '📰', category: 'news' },
    { appName: 'BBC News', packageName: 'bbc.mobile.news.ww', icon: '📻', category: 'news' },
    { appName: 'New York Times', packageName: 'com.nytimes.android', icon: '📄', category: 'news' },

    // Shopping
    { appName: 'Amazon', packageName: 'com.amazon.mShop.android.shopping', icon: '📦', category: 'shopping' },
    { appName: 'eBay', packageName: 'com.ebay.mobile', icon: '🛒', category: 'shopping' },
    { appName: 'AliExpress', packageName: 'com.alibaba.aliexpresshd', icon: '🛍️', category: 'shopping' },

    // Productivity (usually not blocked)
    { appName: 'Gmail', packageName: 'com.google.android.gm', icon: '📧', category: 'productivity' },
    { appName: 'Google Drive', packageName: 'com.google.android.apps.docs', icon: '💾', category: 'productivity' },
    { appName: 'Microsoft Office', packageName: 'com.microsoft.office.officehubrow', icon: '📊', category: 'productivity' },
    { appName: 'Slack', packageName: 'com.Slack', icon: '💬', category: 'productivity' },
    { appName: 'Zoom', packageName: 'us.zoom.videomeetings', icon: '📹', category: 'productivity' },
  ];

  async getInstalledApps(): Promise<InstalledApp[]> {
    // For now, return common apps since we can't access actual installed apps
    // In a real implementation, you would use native modules to get actual installed apps
    if (Platform.OS === 'android') {
      // On Android, you could use a native module to get installed apps
      return this.getAndroidApps();
    } else if (Platform.OS === 'ios') {
      // On iOS, app access is more restricted, so we use common apps
      return this.getIOSApps();
    } else {
      // Web platform
      return this.getWebApps();
    }
  }

  private async getAndroidApps(): Promise<InstalledApp[]> {
    // In a real implementation, this would use a native Android module
    // For now, return common Android apps
    return this.commonApps.filter(app => 
      !app.packageName.includes('apple') && 
      app.category !== 'productivity'
    );
  }

  private async getIOSApps(): Promise<InstalledApp[]> {
    // iOS has more restrictions, so we return common iOS apps
    return this.commonApps.map(app => ({
      ...app,
      packageName: app.packageName.replace('com.', 'ios.').replace('android', 'ios')
    })).filter(app => app.category !== 'productivity');
  }

  private async getWebApps(): Promise<InstalledApp[]> {
    // For web, return web-based apps/services
    return this.commonApps.filter(app => app.category !== 'productivity');
  }

  async searchApps(query: string): Promise<InstalledApp[]> {
    const installedApps = await this.getInstalledApps();
    const searchTerm = query.toLowerCase();
    
    return installedApps.filter(app =>
      app.appName.toLowerCase().includes(searchTerm) ||
      app.packageName.toLowerCase().includes(searchTerm) ||
      (app.category && app.category.toLowerCase().includes(searchTerm))
    );
  }

  async getAppSuggestionsByCategory(category: AppCategory): Promise<InstalledApp[]> {
    const installedApps = await this.getInstalledApps();
    return installedApps.filter(app => app.category === category);
  }

  async getPopularDistractionApps(): Promise<InstalledApp[]> {
    const installedApps = await this.getInstalledApps();
    const distractingCategories: AppCategory[] = ['social', 'entertainment', 'games', 'news', 'shopping'];
    
    return installedApps.filter(app => 
      app.category && distractingCategories.includes(app.category as AppCategory)
    );
  }

  async isAppInstalled(packageName: string): Promise<boolean> {
    const installedApps = await this.getInstalledApps();
    return installedApps.some(app => app.packageName === packageName);
  }

  getAppIcon(packageName: string): string {
    const app = this.commonApps.find(app => app.packageName === packageName);
    return app?.icon || '📱';
  }

  getAppCategory(packageName: string): AppCategory {
    const app = this.commonApps.find(app => app.packageName === packageName);
    return (app?.category as AppCategory) || 'other';
  }
}

export const installedAppsService = new InstalledAppsService();
