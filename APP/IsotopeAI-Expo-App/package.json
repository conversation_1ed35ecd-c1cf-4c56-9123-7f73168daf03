{"name": "isotope-ai", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.4.2", "@supabase/supabase-js": "^2.50.2", "expo": "^53.0.13", "expo-application": "^6.1.4", "expo-blur": "~14.1.5", "expo-camera": "~16.1.9", "expo-constants": "~17.1.6", "expo-device": "^7.1.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-notifications": "^0.31.3", "expo-router": "~5.1.1", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.523.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-device-activity": "^0.5.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.27.4", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}