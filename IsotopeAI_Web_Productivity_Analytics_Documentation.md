# IsotopeAI Web - Productivity & Analytics System Documentation

## Overview
This document provides a comprehensive analysis of the productivity and analytics features in IsotopeAI Web application, detailing the complete user journey from starting a timer to viewing analytics, data structures, Supabase integration, and all related components.

## Table of Contents
1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [User Journey Flow](#user-journey-flow)
4. [Core Components](#core-components)
5. [Analytics Features](#analytics-features)
6. [Data Flow](#data-flow)
7. [Supabase Integration](#supabase-integration)
8. [Timer Functionality](#timer-functionality)
9. [Subject Management](#subject-management)
10. [Implementation Details](#implementation-details)

## System Architecture

### Core Technologies
- **Frontend**: React + TypeScript + Vite
- **Database**: Supabase (PostgreSQL)
- **State Management**: Zustand + React Context
- **UI Components**: Shadcn/ui + Tailwind CSS
- **Charts**: MUI X-Charts
- **Real-time**: Supabase Realtime subscriptions

### Key Directories
```
WEB/src/
├── components/
│   ├── productivity/          # Timer and productivity components
│   └── analytics/            # Analytics visualization components
├── pages/
│   ├── Productivity.tsx      # Main productivity page
│   └── Analytics.tsx         # Analytics dashboard
├── stores/                   # Zustand state management
├── utils/                    # Utility functions
└── types/                    # TypeScript definitions
```

## Database Schema

### Primary Tables

#### 1. study_sessions
```sql
CREATE TABLE study_sessions (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    user_id TEXT NOT NULL,
    subject TEXT,
    task_name TEXT,
    task_type TEXT,
    duration INTEGER NOT NULL,           -- Duration in SECONDS
    mode TEXT CHECK (mode IN ('pomodoro', 'stopwatch')),
    phase TEXT CHECK (phase IN ('work', 'shortBreak', 'longBreak')),
    completed BOOLEAN DEFAULT FALSE,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    date TEXT NOT NULL,                  -- YYYY-MM-DD format
    notes TEXT,
    feedback TEXT,
    productivity_rating INTEGER,         -- 1-5 scale
    subject_color TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. userSubjects
```sql
CREATE TABLE userSubjects (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    userId TEXT NOT NULL,
    name TEXT NOT NULL,
    color TEXT,
    createdAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. users (Extended Profile)
```sql
-- Additional columns for productivity features
ALTER TABLE users ADD COLUMN daily_target INTEGER;        -- Daily study target in minutes
ALTER TABLE users ADD COLUMN daily_motivation TEXT;       -- Daily motivation message
ALTER TABLE users ADD COLUMN day_start_time INTEGER;      -- Hour when day resets (0-23)
```

## User Journey Flow

### 1. Starting a Study Session

#### Step 1: Access Productivity Page
- User navigates to `/productivity`
- Page loads with timer interface and subject selection

#### Step 2: Subject Selection
- User selects existing subject or creates new one
- Subject data stored in `userSubjects` table
- Subject includes name and color for visualization

#### Step 3: Timer Mode Selection
- **Stopwatch Mode**: Counts up from 0
- **Pomodoro Mode**: Counts down from set duration (default 25 minutes)

#### Step 4: Task Definition (Optional)
- User can specify task name and task type
- Task types: "Lecture", "Exercise", "Reading", "Practice", "Review", "General Study", "Custom"
User can specify tast description

#### Step 5: Start Timer
- Timer begins counting
- Session data temporarily stored in sessionStorage
- Web Worker handles timer ticks for accuracy

### 2. During Study Session

#### Real-time Features
- Live timer display with validation
- Pause/resume, end session functionality
- Session persistence across page refreshes
- Picture-in-Picture mode support
- Fullscreen mode
- Notification system on regular interavls

#### Session Management
- Auto-save session state to sessionStorage
- Handle browser close/refresh warnings
- Spotify integration for background music
- When session paused, show how much studied till now. Option to add quick note(Why paused, whats next?) 

### 3. Completing a Study Session

#### Session Summary Dialog
- User provides feedback (optional)
- Productivity rating (1-5 scale)
- User writes Session summary
- Option to mark as completed or paused

#### Data Persistence
- Session saved to Supabase `study_sessions` table
- Real-time analytics updates
- Local cache invalidation

## Core Components

### Timer Components

#### 1. StudyTimer.tsx
**Location**: `WEB/src/components/productivity/StudyTimer.tsx`
**Purpose**: Main timer component with full functionality

**Key Features**:
- Dual mode support (Pomodoro/Stopwatch)
- Session persistence via sessionStorage
- Real-time updates using Web Worker
- Picture-in-Picture support
- Notification system

**State Management**:
```typescript
interface SavedTimerState {
  status: TimerStatus;
  startTime: number | null;
  accumulatedPausedTime: number;
  pauseStartTime: number | null;
  currentPhase: PhaseType;
  completedSessions: number;
  mode: TimerMode;
  selectedSubject: string;
  taskName: string;
  taskType: string;
  saveTimestamp: number;
}
```

#### 2. TimerDisplay.tsx
**Purpose**: Displays formatted time with validation
**Features**:
- Time validation (prevents negative/unrealistic values)
- Responsive font sizing
- Theme-aware styling

#### 3. TimerControls.tsx
**Purpose**: Timer control buttons and actions
**Features**:
- Start/Pause/Reset functionality
- Complete session button
- Fullscreen toggle
- Animated button states

### Subject Management

#### 1. SubjectManager.tsx
**Purpose**: CRUD operations for user subjects
**Features**:
- Create, edit, delete subjects
- Color picker integration
- Real-time Supabase sync
- Dropdown selection interface

#### 2. Subject Store (Zustand)
**Location**: `WEB/src/stores/supabaseSubjectStore.ts`
**Features**:
- Persistent state management
- Supabase integration
- Real-time subscriptions
- Cache management

## Analytics Features

### Analytics Dashboard Structure

#### 1. Overview Tab
**Components**: Daily stats, calendar view, streak tracking
**Metrics**:
- Total study time today
- Daily target progress
- Study streak information
- Subject distribution
- Recent activity trends

#### 2. Daily Tab
**Components**: Day-specific analysis
**Metrics**:
- Hourly breakdown
- Session details
- Subject-wise time distribution
- Productivity ratings

#### 3. Weekly Tab
**Components**: Week-over-week analysis
**Metrics**:
- Weekly totals
- Day-of-week patterns
- Subject consistency
- Goal achievement

#### 4. Monthly Tab
**Components**: Long-term trends
**Metrics**:
- Monthly totals
- Growth trends
- Subject evolution
- Productivity patterns

#### 5. Subjects Tab
**Components**: Subject-specific analytics
**Metrics**:
- Time per subject
- Average session duration
- Subject productivity ratings
- Recent activity per subject

#### 6. Task Types Tab
**Components**: Task type analysis
**Metrics**:
- Time distribution by task type
- Average productivity by task type
- Task type trends

### Analytics Data Processing

#### Data Aggregation
```typescript
interface Analytics {
  dailyStats: DailyStat[];
  weeklyStats: WeeklyStat[];
  monthlyStats: MonthlyStat[];
  subjectStats: SubjectStat[];
  taskTypeStats: TaskTypeStat[];
}
```

#### Key Metrics Calculation
- **Total Duration**: Sum of all session durations
- **Average Session**: Total time / number of sessions
- **Productivity Score**: Weighted average of ratings
- **Streak Calculation**: Consecutive days with study time
- **Target Achievement**: Daily time vs. target comparison

## Data Flow

### 1. Timer Session Creation
```
User Input → StudyTimer → sessionStorage → Supabase → Analytics Update
```

### 2. Real-time Updates
```
Timer Tick → Web Worker → State Update → UI Refresh
```

### 3. Analytics Processing
```
Raw Sessions → Data Aggregation → Chart Generation → UI Display
```

### 4. Subject Management
```
User Action → Subject Store → Supabase → Real-time Sync → UI Update
```

## Supabase Integration

### Authentication
- Uses Supabase Auth for user management
- Row Level Security (RLS) policies ensure data privacy
- Session-based authentication

### Real-time Features
- Supabase Realtime for live updates
- Subject changes sync across devices
- Analytics refresh on data changes

### Data Validation
- Server-side validation via PostgreSQL constraints
- Client-side validation before submission
- Error handling and retry logic

## Timer Functionality

### Time Tracking Accuracy
- Web Worker prevents main thread blocking
- 500ms tick interval for smooth updates
- Session persistence handles interruptions
- Time validation prevents data corruption

### Session Management
- Automatic session recovery on page reload
- Browser warning on active session close
- Pause tracking for accurate duration calculation
- Multiple session support (pause/resume)

### Notification System
- Configurable notification intervals
- Browser notification API integration
- Audio alerts for session completion
- Visual indicators for timer state

## Subject Management

### CRUD Operations
- **Create**: Add new subjects with color selection
- **Read**: Fetch user subjects from Supabase
- **Update**: Edit subject name and color
- **Delete**: Remove subjects with confirmation

### Data Synchronization
- Real-time sync across devices
- Offline support with local storage fallback
- Conflict resolution for concurrent edits
- Migration from local to Supabase storage

### Color Management
- Predefined color palette
- Custom color picker
- Consistent color usage across analytics
- Accessibility-compliant color choices

## Implementation Details

### Performance Optimizations
- Lazy loading of analytics components
- Memoized calculations for expensive operations
- Efficient chart rendering with MUI X-Charts
- Debounced user input handling

### Error Handling
- Graceful degradation on Supabase errors
- Local storage fallback mechanisms
- User-friendly error messages
- Automatic retry logic for failed operations

### Accessibility
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management for modals

### Mobile Responsiveness
- Responsive design for all screen sizes
- Touch-friendly interface elements
- Optimized chart rendering for mobile
- Progressive enhancement approach

## Detailed Analytics Components

### 1. StudyCalendar Component
**Purpose**: Visual calendar showing daily study activity
**Features**:
- Heat map visualization of daily study time
- Click to view day details
- Streak highlighting
- Target achievement indicators
- Month/year navigation

**Data Structure**:
```typescript
interface DailyStat {
  date: string;              // YYYY-MM-DD
  totalDuration: number;     // seconds
  targetAchieved: boolean;
  sessionCount: number;
  subjectDurations: { [subject: string]: number };
  taskTypeDurations: { [taskType: string]: number };
}
```

### 2. Chart Components

#### Bar Charts (Daily/Weekly/Monthly)
- Time distribution by subject
- Daily progress vs targets
- Comparative analysis across periods

#### Line Charts
- Trend analysis over time
- Progress tracking
- Productivity score evolution

#### Pie Charts
- Subject time distribution
- Task type breakdown
- Session completion rates

### 3. Session Detail Views

#### Day Detail Modal
**Features**:
- List of all sessions for selected day
- Edit/delete session functionality
- Session timeline view
- Subject and task type filtering

**Session Edit Capabilities**:
- Modify duration, subject, task name
- Update productivity rating
- Add/edit notes and feedback
- Change task type classification

### 4. Productivity Metrics

#### Streak Calculation
```typescript
interface StreakInfo {
  currentStreak: number;     // consecutive days
  longestStreak: number;     // all-time record
  streakStartDate: string;   // when current streak began
  isActiveToday: boolean;    // studied today
}
```

#### Target Management
- Daily study time targets (in minutes)
- Target achievement tracking
- Progress visualization
- Motivational messaging system

#### Productivity Ratings
- 1-5 scale session ratings
- Average productivity by subject
- Productivity trends over time
- Correlation with study duration

## Advanced Features

### 1. Focus Analytics
**Purpose**: Deep analysis of study patterns and effectiveness

**Metrics Tracked**:
- Session completion rates
- Average session duration by subject
- Time of day productivity patterns
- Break frequency and duration
- Distraction indicators

### 2. Leaderboard Integration
**Purpose**: Gamification and social motivation
**Features**:
- Weekly/monthly study time rankings
- Subject-specific leaderboards
- Achievement badges
- Progress sharing capabilities

### 3. Export Functionality
**Purpose**: Data portability and external analysis
**Formats**:
- CSV export of session data
- PDF reports with charts
- Image export of analytics charts
- JSON data export for backup

### 4. Goal Setting and Tracking
**Purpose**: Long-term motivation and planning

**Goal Types**:
- Daily study time targets
- Subject-specific goals
- Exam preparation milestones
- Weekly/monthly objectives

**Tracking Features**:
- Progress visualization
- Achievement notifications
- Goal adjustment recommendations
- Historical goal performance

## Data Validation and Quality

### Input Validation
- Duration limits (max 24 hours per session)
- Date format validation (YYYY-MM-DD)
- Subject name constraints
- Rating range validation (1-5)

### Data Integrity
- Duplicate session prevention
- Orphaned data cleanup
- Consistency checks across tables
- Automatic data migration scripts

### Error Recovery
- Session recovery from browser crashes
- Data backup and restore mechanisms
- Conflict resolution for concurrent edits
- Graceful handling of network failures

## Performance Considerations

### Database Optimization
- Indexed queries on user_id and date
- Efficient aggregation queries
- Pagination for large datasets
- Query result caching

### Frontend Optimization
- Virtual scrolling for large lists
- Lazy loading of chart components
- Memoized calculations
- Debounced user interactions

### Real-time Updates
- Selective data refresh
- Optimistic UI updates
- Background sync mechanisms
- Conflict resolution strategies

## Security and Privacy

### Data Protection
- Row Level Security (RLS) policies
- User data isolation
- Encrypted data transmission
- Secure session management

### Privacy Features
- Data anonymization options
- Export/delete user data
- Privacy-compliant analytics
- Opt-out mechanisms

## Key Implementation Examples

### 1. Timer State Management
```typescript
// Session storage persistence
const saveStateToSessionStorage = useCallback(() => {
  const stateToSave: SavedTimerState = {
    status: timerStatus,
    startTime: startTime,
    accumulatedPausedTime: accumulatedPausedTime,
    pauseStartTime: pauseStartTimeRef.current,
    currentPhase: currentPhase,
    completedSessions: completedSessions,
    mode: mode,
    selectedSubject: selectedSubject?.name || "",
    saveTimestamp: Date.now(),
    taskName: taskName,
    taskType: taskType,
  };
  sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(stateToSave));
}, [/* dependencies */]);

// Session recovery on page load
const restoreStateFromSessionStorage = useCallback(() => {
  const savedState = sessionStorage.getItem(SESSION_STORAGE_KEY);
  if (savedState) {
    const parsed: SavedTimerState = JSON.parse(savedState);
    // Restore timer state with validation
    const timeSinceSave = Date.now() - parsed.saveTimestamp;
    // Apply time corrections and restore state
  }
}, []);
```

### 2. Supabase Session Saving
```typescript
const saveStudySessionToSupabase = async (
  duration: number,
  completed: boolean,
  feedback?: string,
  productivityRating?: number
) => {
  const sessionData = {
    id: sessionStartTimeRef.current.toString(),
    user_id: user.id,
    subject: selectedSubject.name,
    task_name: taskName || "Study Session",
    task_type: taskType,
    start_time: formattedStartTime,
    end_time: formattedEndTime,
    duration: Math.round(duration), // Duration in seconds
    mode: modeInternal,
    phase: currentPhase,
    completed,
    date: formatDateToLocalYYYYMMDD(adjustedDate),
    notes: feedback || "",
    productivity_rating: productivityRating || 0
  };

  await saveStudySession(sessionData);
};
```

### 3. Analytics Data Processing
```typescript
const processAnalytics = (sessions: StudySession[]): Analytics => {
  const dailyStats: { [key: string]: any } = {}
  const weeklyStats: { [key: string]: any } = {}
  const monthlyStats: { [key: string]: any } = {}
  const subjectStats: { [key: string]: any } = {}
  const taskTypeStats: { [key: string]: any } = {}

  sessions.forEach(session => {
    // Skip invalid sessions
    if (!session.duration || session.duration < 0 || !session.date || !session.subject) return

    const taskType = session.taskType || "Study"

    // Daily stats aggregation
    if (!dailyStats[session.date]) {
      dailyStats[session.date] = {
        date: session.date,
        totalDuration: 0,
        subjectDurations: {},
        taskTypeDurations: {},
        completedPomodoros: 0
      }
    }

    dailyStats[session.date].totalDuration += session.duration
    dailyStats[session.date].subjectDurations[session.subject] =
      (dailyStats[session.date].subjectDurations[session.subject] || 0) + session.duration

    // Similar processing for weekly, monthly, subject, and task type stats
  });

  return {
    dailyStats: Object.values(dailyStats),
    weeklyStats: Object.values(weeklyStats),
    monthlyStats: Object.values(monthlyStats),
    subjectStats: Object.values(subjectStats),
    taskTypeStats: Object.values(taskTypeStats)
  };
};
```

### 4. Real-time Timer Updates
```typescript
// Web Worker for accurate timing
useEffect(() => {
  if (typeof Worker !== 'undefined') {
    const worker = new Worker('/timerWorker.js');

    worker.onmessage = (e) => {
      if (e.data.type === 'TICK' && timerStatus === 'running') {
        // Update timer display
        setCurrentTime(Date.now());
      }
    };

    if (timerStatus === 'running') {
      worker.postMessage({ type: 'START' });
    } else {
      worker.postMessage({ type: 'STOP' });
    }

    return () => worker.terminate();
  }
}, [timerStatus]);
```

### 5. Subject Management with Supabase
```typescript
const addSubject = async (userId: string, name: string, color: string) => {
  try {
    const subjectId = uuidv4();
    const subjectData = {
      id: subjectId,
      userId,
      name,
      color,
      createdAt: new Date().toISOString(),
    };

    const createdSubject = await createUserSubject(subjectData);

    // Update local state
    const currentSubjects = get().subjects;
    set({
      subjects: [...currentSubjects, convertToSubject(createdSubject)],
      lastFetched: Date.now()
    });

    return convertToSubject(createdSubject);
  } catch (error) {
    console.error('Error adding subject:', error);
    throw error;
  }
};
```

## Mobile App Implementation Considerations

### 1. React Native Adaptations
- Replace Web Workers with React Native background tasks
- Use AsyncStorage instead of sessionStorage
- Implement native notifications
- Handle app state changes (background/foreground)

### 2. Navigation Structure
```
App Navigator
├── Productivity Stack
│   ├── Timer Screen
│   ├── Subject Management
│   └── Session Summary
└── Analytics Stack
    ├── Overview Tab
    ├── Daily Tab
    ├── Weekly Tab
    ├── Monthly Tab
    ├── Subjects Tab
    └── Task Types Tab
```

### 3. State Management
- Use React Context + useReducer or Zustand
- Implement offline-first approach
- Sync with Supabase when online
- Handle network connectivity changes

### 4. UI Components
- Use React Native Paper or NativeBase for Material Design
- Implement custom chart components or use Victory Native
- Ensure accessibility compliance
- Support dark/light themes

### 5. Performance Optimizations
- Use FlatList for large data sets
- Implement lazy loading for analytics
- Cache frequently accessed data
- Optimize image and asset loading

This comprehensive documentation provides all necessary details for implementing the productivity and analytics features in the Expo mobile application while maintaining feature parity and consistency with the web version.
