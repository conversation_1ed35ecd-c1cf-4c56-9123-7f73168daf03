import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, CheckSquare, ListTodo, Clock, Tags, Share2, BarChart2, GripVertical, CheckCircle2 } from "lucide-react";
import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header } from "@/components/shared";
import { Footer } from "@/components/shared";
import { Helmet } from "react-helmet";

// Animation variants
const fadeUpVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 1,
      delay: 0.5 + i * 0.2,
      ease: [0.25, 0.4, 0.25, 1],
    },
  }),
};

const TasksLanding = () => {
  const [activeView, setActiveView] = useState<'table' | 'kanban'>('table');
  const [draggedTask, setDraggedTask] = useState<string | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<{show: boolean, message: string}>({show: false, message: ''});
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Hide success message after 2 seconds
  useEffect(() => {
    if (successMessage.show) {
      const timer = setTimeout(() => {
        setSuccessMessage({show: false, message: ''});
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [successMessage.show]);

  const handleGetStarted = () => {
    if (user) {
      navigate('/tasks');
    } else {
      signInWithGoogle();
    }
  };

  const handleDragStart = (taskId: string) => {
    setDraggedTask(taskId);
  };

  const handleDragEnd = () => {
    setDraggedTask(null);
    setDragOverColumn(null);
  };

  const handleDragOver = (column: string) => {
    if (draggedTask && dragOverColumn !== column) {
      setDragOverColumn(column);
    }
  };

  const handleDrop = (column: string) => {
    if (draggedTask) {
      // Show success message
      let taskName = '';
      if (draggedTask === 'math') taskName = 'Math Assignment';
      else if (draggedTask === 'physics-lab') taskName = 'Physics Lab Report';
      else if (draggedTask === 'physics-notes') taskName = 'Physics Notes';
      else if (draggedTask === 'chapter5') taskName = 'Chapter 5';
      
      let columnName = '';
      if (column === 'todo') columnName = 'To Do';
      else if (column === 'inprogress') columnName = 'In Progress';
      else if (column === 'completed') columnName = 'Completed';
      
      setSuccessMessage({
        show: true,
        message: `Moved "${taskName}" to ${columnName}`
      });
    }
    handleDragEnd();
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030303] font-onest flex flex-col min-h-screen">
      <Helmet>
        <title>Task Management for Students | Kanban & Table Views | IsotopeAI</title>
        <meta
          name="description"
          content="Organize, prioritize, and track your academic tasks with our powerful task management system. Switch between table and kanban views for effective task organization."
        />
        <meta
          name="keywords"
          content="task management, student tasks, kanban board, table view, task organization, priority labels, due date tracking, academic tasks, study tasks, task visualization, JEE task management, NEET task planning"
        />
        <meta property="og:title" content="Task Management for Students | Kanban & Table Views | IsotopeAI" />
        <meta property="og:description" content="Organize, prioritize, and track your academic tasks with our powerful task management system. Switch between table and kanban views for effective task organization." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/tasks-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/tasks-landing" />
      </Helmet>

      {/* Header */}
      <Header />

      {/* Background Elements */}
      <GlowingBackground />
      <FloatingElements />
      
      {/* Main Content */}
      <main className="flex-grow relative z-10">
        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-orange-950/20 via-transparent to-red-950/20"></div>

          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Left side - Text content */}
              <motion.div
                initial="hidden"
                animate="visible"
                className="text-center lg:text-left"
              >
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
                >
                  <CheckSquare className="h-4 w-4 text-orange-400" />
                  <span className="font-medium">Task Management</span>
                </motion.div>

                <motion.h1
                  custom={0}
                  variants={fadeUpVariants}
                  className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 md:mb-8 tracking-tight leading-tight"
                >
                  <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                    Manage Your{" "}
                  </span>
                  <motion.span
                    className="relative bg-clip-text text-transparent bg-gradient-to-r from-orange-400 via-red-400 to-rose-400"
                    initial={{ backgroundPosition: "0% 50%" }}
                    animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                    transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                  >
                    Study Tasks
                  </motion.span>
                  <br />
                  <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                    Effectively
                  </span>
                </motion.h1>

                <motion.p
                  custom={1}
                  variants={fadeUpVariants}
                  className="text-lg sm:text-xl md:text-2xl text-white/60 mb-8 md:mb-12 max-w-3xl mx-auto lg:mx-0 leading-relaxed"
                >
                  Organize, prioritize, and track your academic tasks with our powerful task management system
                </motion.p>

                <motion.div
                  custom={2}
                  variants={fadeUpVariants}
                  className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mb-8"
                >
                  {user ? (
                    <Button
                      asChild
                      className="bg-orange-500/80 hover:bg-orange-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                    >
                      <div onClick={handleGetStarted} className="cursor-pointer">
                        <span className="relative z-10 flex items-center">
                          Go to Task Manager
                          <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-orange-600 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </Button>
                  ) : (
                    <Button
                      asChild
                      className="bg-orange-500/80 hover:bg-orange-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                    >
                      <div onClick={handleGetStarted} className="cursor-pointer">
                        <span className="relative z-10 flex items-center">
                          Try now (It's 100% FREE)
                          <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-orange-600 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </Button>
                  )}
                </motion.div>

                {/* Trust indicators */}
                <motion.div
                  custom={3}
                  variants={fadeUpVariants}
                  className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-white/60"
                >
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                    <span>Table & Kanban views</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    <span>Priority tracking</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-rose-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                    <span>Due date alerts</span>
                  </div>
                </motion.div>
              </motion.div>
            
              {/* Right side - Task Demo */}
              <motion.div
                custom={4}
                variants={fadeUpVariants}
                className="lg:mt-0 mt-8"
              >
                <div className="bg-white/[0.03] backdrop-blur-md rounded-2xl p-6 md:p-8 shadow-2xl border border-white/[0.08] relative overflow-hidden">
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 via-transparent to-red-500/10 pointer-events-none"></div>

                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <div className="bg-gradient-to-br from-orange-500/30 to-red-500/30 p-3 rounded-xl border border-white/10">
                          <ListTodo className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-semibold text-white/90">Task Manager</h3>
                          <p className="text-sm text-white/60">Table & Kanban Views</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <span className={`px-3 py-1.5 text-xs rounded-full transition-all duration-300 font-medium ${activeView === 'table' ? 'bg-orange-500/20 text-orange-400 ring-1 ring-orange-500/30' : 'bg-white/5 text-white/60 hover:bg-white/10'}`}>Table</span>
                        <span className={`px-3 py-1.5 text-xs rounded-full transition-all duration-300 font-medium ${activeView === 'kanban' ? 'bg-red-500/20 text-red-400 ring-1 ring-red-500/30' : 'bg-white/5 text-white/60 hover:bg-white/10'}`}>Kanban</span>
                      </div>
                    </div>
                
                    {/* Tabs for Table/Kanban */}
                    <div className="flex border-b border-white/10 mb-6">
                      <button
                        className={`px-4 py-3 text-sm font-medium transition-all duration-300 relative ${activeView === 'table' ? 'text-orange-400' : 'text-white/60 hover:text-white/80'}`}
                        onClick={() => setActiveView('table')}
                      >
                        Table View
                        {activeView === 'table' && (
                          <motion.div
                            layoutId="activeTab"
                            className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-orange-400 to-red-400"
                            initial={false}
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                          />
                        )}
                      </button>
                      <button
                        className={`px-4 py-3 text-sm font-medium transition-all duration-300 relative ${activeView === 'kanban' ? 'text-red-400' : 'text-white/60 hover:text-white/80'}`}
                        onClick={() => setActiveView('kanban')}
                      >
                        Kanban Board
                        {activeView === 'kanban' && (
                          <motion.div
                            layoutId="activeTab"
                            className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-red-400 to-rose-400"
                            initial={false}
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                          />
                        )}
                      </button>
                    </div>

                    {/* Table View Demo */}
                    {activeView === 'table' && (
                      <motion.div
                        className="space-y-3 mb-6"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="flex items-center justify-between p-4 bg-white/[0.02] rounded-xl border border-white/5 hover:border-white/10 transition-colors">
                          <div className="flex items-center gap-3">
                            <input type="checkbox" className="w-4 h-4 accent-orange-500 rounded" />
                            <div>
                              <h4 className="font-medium text-white/90">Complete Math Assignment</h4>
                              <p className="text-xs text-white/60">Due in 2 hours</p>
                            </div>
                          </div>
                          <span className="px-3 py-1 text-xs bg-red-500/20 text-red-400 rounded-full font-medium">High</span>
                        </div>

                        <div className="flex items-center justify-between p-4 bg-white/[0.02] rounded-xl border border-white/5 hover:border-white/10 transition-colors">
                          <div className="flex items-center gap-3">
                            <input type="checkbox" className="w-4 h-4 accent-orange-500 rounded" />
                            <div>
                              <h4 className="font-medium text-white/90">Review Physics Notes</h4>
                              <p className="text-xs text-white/60">Due today at 6 PM</p>
                            </div>
                          </div>
                          <span className="px-3 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full font-medium">Medium</span>
                        </div>

                        <div className="flex items-center justify-between p-4 bg-white/[0.02] rounded-xl border border-white/5 hover:border-white/10 transition-colors">
                          <div className="flex items-center gap-3">
                            <input type="checkbox" className="w-4 h-4 accent-orange-500 rounded" checked />
                            <div className="line-through opacity-60">
                              <h4 className="font-medium text-white/90">Read Chapter 5</h4>
                              <p className="text-xs text-white/60">Completed</p>
                            </div>
                          </div>
                          <span className="px-3 py-1 text-xs bg-green-500/20 text-green-400 rounded-full font-medium">Low</span>
                        </div>
                      </motion.div>
                    )}
                
                    {/* Kanban Board View */}
                    {activeView === 'kanban' && (
                      <motion.div
                        className="mb-6"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="flex gap-3 overflow-x-auto pb-2">
                          {/* To Do Column */}
                          <div
                            className={`flex-shrink-0 w-[32%] bg-white/[0.02] rounded-xl p-3 transition-all duration-300 border ${dragOverColumn === 'todo' ? 'bg-orange-500/10 border-orange-500/30 shadow-lg' : 'border-white/5 hover:border-white/10'}`}
                            onDragOver={(e) => {
                              e.preventDefault();
                              handleDragOver('todo');
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              handleDrop('todo');
                            }}
                          >
                            <div className="text-xs font-medium mb-3 px-1 flex items-center justify-between">
                              <span className="text-white/80">To Do</span>
                              <span className="bg-orange-500/20 text-orange-400 text-xs px-2 py-1 rounded-full font-medium">2</span>
                            </div>
                        
                            <div className="space-y-2">
                              <motion.div
                                className={`bg-white/[0.03] rounded-lg p-3 border shadow-lg cursor-move relative backdrop-blur-sm transition-all duration-200 ${draggedTask === 'math' ? 'opacity-50 border-dashed border-orange-500/50 scale-95' : 'border-white/10 hover:border-white/20 hover:bg-white/[0.05]'}`}
                                whileHover={{ scale: draggedTask === 'math' ? 0.95 : 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                draggable
                                onDragStart={() => handleDragStart('math')}
                                onDragEnd={handleDragEnd}
                              >
                                <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-orange-400/60">
                                  <GripVertical size={14} />
                                </div>
                                <h4 className="text-xs font-medium text-white/90 mb-2">Complete Math Assignment</h4>
                                <div className="flex items-center justify-between">
                                  <p className="text-[10px] text-white/60">Due in 2 hours</p>
                                  <span className="px-2 py-0.5 text-[10px] bg-red-500/20 text-red-400 rounded-full font-medium">High</span>
                                </div>
                              </motion.div>

                              <motion.div
                                className={`bg-white/[0.03] rounded-lg p-3 border shadow-lg cursor-move relative backdrop-blur-sm transition-all duration-200 ${draggedTask === 'physics-lab' ? 'opacity-50 border-dashed border-orange-500/50 scale-95' : 'border-white/10 hover:border-white/20 hover:bg-white/[0.05]'}`}
                                whileHover={{ scale: draggedTask === 'physics-lab' ? 0.95 : 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                draggable
                                onDragStart={() => handleDragStart('physics-lab')}
                                onDragEnd={handleDragEnd}
                              >
                                <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-orange-400/60">
                                  <GripVertical size={14} />
                                </div>
                                <h4 className="text-xs font-medium text-white/90 mb-2">Physics Lab Report</h4>
                                <div className="flex items-center justify-between">
                                  <p className="text-[10px] text-white/60">Due tomorrow</p>
                                  <span className="px-2 py-0.5 text-[10px] bg-yellow-500/20 text-yellow-400 rounded-full font-medium">Medium</span>
                                </div>
                              </motion.div>
                            </div>
                          </div>
                      
                          {/* In Progress Column */}
                          <div
                            className={`flex-shrink-0 w-[32%] bg-white/[0.02] rounded-xl p-3 transition-all duration-300 border ${dragOverColumn === 'inprogress' ? 'bg-red-500/10 border-red-500/30 shadow-lg' : 'border-white/5 hover:border-white/10'}`}
                            onDragOver={(e) => {
                              e.preventDefault();
                              handleDragOver('inprogress');
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              handleDrop('inprogress');
                            }}
                          >
                            <div className="text-xs font-medium mb-3 px-1 flex items-center justify-between">
                              <span className="text-white/80">In Progress</span>
                              <span className="bg-red-500/20 text-red-400 text-xs px-2 py-1 rounded-full font-medium">1</span>
                            </div>

                            <div className="space-y-2">
                              <motion.div
                                className={`bg-white/[0.03] rounded-lg p-3 border shadow-lg cursor-move relative backdrop-blur-sm transition-all duration-200 ${draggedTask === 'physics-notes' ? 'opacity-50 border-dashed border-red-500/50 scale-95' : 'border-white/10 hover:border-white/20 hover:bg-white/[0.05]'}`}
                                whileHover={{ scale: draggedTask === 'physics-notes' ? 0.95 : 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                draggable
                                onDragStart={() => handleDragStart('physics-notes')}
                                onDragEnd={handleDragEnd}
                              >
                                <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-red-400/60">
                                  <GripVertical size={14} />
                                </div>
                                <h4 className="text-xs font-medium text-white/90 mb-2">Review Physics Notes</h4>
                                <div className="flex items-center justify-between">
                                  <p className="text-[10px] text-white/60">Due today at 6 PM</p>
                                  <span className="px-2 py-0.5 text-[10px] bg-yellow-500/20 text-yellow-400 rounded-full font-medium">Medium</span>
                                </div>
                              </motion.div>
                            </div>
                          </div>
                      
                          {/* Completed Column */}
                          <div
                            className={`flex-shrink-0 w-[32%] bg-white/[0.02] rounded-xl p-3 transition-all duration-300 border ${dragOverColumn === 'completed' ? 'bg-green-500/10 border-green-500/30 shadow-lg' : 'border-white/5 hover:border-white/10'}`}
                            onDragOver={(e) => {
                              e.preventDefault();
                              handleDragOver('completed');
                            }}
                            onDrop={(e) => {
                              e.preventDefault();
                              handleDrop('completed');
                            }}
                          >
                            <div className="text-xs font-medium mb-3 px-1 flex items-center justify-between">
                              <span className="text-white/80">Completed</span>
                              <span className="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded-full font-medium">1</span>
                            </div>

                            <div className="space-y-2">
                              <motion.div
                                className={`bg-white/[0.03] rounded-lg p-3 border shadow-lg cursor-move relative backdrop-blur-sm transition-all duration-200 opacity-70 ${draggedTask === 'chapter5' ? 'opacity-40 border-dashed border-green-500/50 scale-95' : 'border-white/10 hover:border-white/20 hover:bg-white/[0.05]'}`}
                                whileHover={{ scale: draggedTask === 'chapter5' ? 0.95 : 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                draggable
                                onDragStart={() => handleDragStart('chapter5')}
                                onDragEnd={handleDragEnd}
                              >
                                <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-green-400/60">
                                  <GripVertical size={14} />
                                </div>
                                <h4 className="text-xs font-medium text-white/90 mb-2 line-through">Read Chapter 5</h4>
                                <div className="flex items-center justify-between">
                                  <p className="text-[10px] text-white/60">Completed</p>
                                  <span className="px-2 py-0.5 text-[10px] bg-green-500/20 text-green-400 rounded-full font-medium">Low</span>
                                </div>
                              </motion.div>
                            </div>
                          </div>
                        </div>

                        {/* Drag Instructions */}
                        <div className="mt-4 text-center">
                          <p className="text-xs text-white/60 italic">Drag tasks between columns to change their status</p>
                        </div>

                        {/* Success Message */}
                        <AnimatePresence>
                          {successMessage.show && (
                            <motion.div
                              className="mt-4 bg-green-500/20 border border-green-500/30 text-green-400 rounded-xl p-3 flex items-center justify-center gap-2 backdrop-blur-sm"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              transition={{ duration: 0.3 }}
                            >
                              <CheckCircle2 size={16} />
                              <span className="text-sm font-medium">{successMessage.message}</span>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    )}
                
                    <div className="mt-6 flex justify-between items-center">
                      <div className="text-sm text-white/60">
                        <span className="font-medium text-orange-400">3/8</span> tasks completed today
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-full text-xs px-4 py-2 h-auto bg-white/5 border-white/10 text-white/80 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                      >
                        View All
                      </Button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-orange-950/10 to-transparent"></div>

          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <motion.h2
                custom={0}
                variants={fadeUpVariants}
                className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Powerful Features for{" "}
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-400 via-red-400 to-rose-400">
                  Task Management
                </span>
              </motion.h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              <FeatureCard
                icon={<ListTodo className="w-6 h-6" />}
                title="Table View"
                description="Organize tasks in a clean, sortable table with priority indicators"
                delay={0.1}
                gradient="from-orange-500/30 to-red-500/30"
              />
              <FeatureCard
                icon={<CheckSquare className="w-6 h-6" />}
                title="Kanban Board"
                description="Visualize your workflow with drag-and-drop task management"
                delay={0.2}
                gradient="from-red-500/30 to-rose-500/30"
              />
              <FeatureCard
                icon={<Tags className="w-6 h-6" />}
                title="Priority Labels"
                description="Categorize tasks with customizable priority levels"
                delay={0.3}
                gradient="from-rose-500/30 to-pink-500/30"
              />
              <FeatureCard
                icon={<Clock className="w-6 h-6" />}
                title="Due Date Tracking"
                description="Never miss deadlines with clear due date indicators"
                delay={0.4}
                gradient="from-orange-500/30 to-amber-500/30"
              />
              <FeatureCard
                icon={<Share2 className="w-6 h-6" />}
                title="Group Integration"
                description="Share tasks with your study groups for collaborative work"
                delay={0.5}
                gradient="from-red-500/30 to-orange-500/30"
              />
              <FeatureCard
                icon={<BarChart2 className="w-6 h-6" />}
                title="Progress Analytics"
                description="Track completion rates and productivity patterns over time"
                delay={0.6}
                gradient="from-rose-500/30 to-red-500/30"
              />
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-red-950/10 via-transparent to-orange-950/10"></div>

          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <motion.h2
                custom={0}
                variants={fadeUpVariants}
                className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Key{" "}
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-400 via-rose-400 to-orange-400">
                  Benefits
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  {" "}for Students
                </span>
              </motion.h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
              <BenefitCard
                title="Flexible Task Views"
                description="Switch between table and kanban views to manage tasks your way"
                delay={0.1}
                gradient="from-orange-500/30 to-red-500/30"
              />
              <BenefitCard
                title="Visual Workflow"
                description="See your progress at a glance with intuitive visual organization"
                delay={0.2}
                gradient="from-red-500/30 to-rose-500/30"
              />
              <BenefitCard
                title="Seamless Integration"
                description="Connect your tasks with other IsotopeAI features for a complete study system"
                delay={0.3}
                gradient="from-rose-500/30 to-orange-500/30"
              />
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-orange-950/20 via-transparent to-red-950/20"></div>
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <div className="container mx-auto px-4 md:px-6 text-center relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="max-w-4xl mx-auto"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
              >
                <CheckSquare className="h-4 w-4 text-orange-400" />
                <span className="font-medium">Ready to Organize?</span>
              </motion.div>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 md:mb-8 tracking-tight leading-tight"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Ready to Visualize{" "}
                </span>
                <motion.span
                  className="relative bg-clip-text text-transparent bg-gradient-to-r from-orange-400 via-red-400 to-rose-400"
                  initial={{ backgroundPosition: "0% 50%" }}
                  animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                  transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                >
                  Your Tasks?
                </motion.span>
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-lg sm:text-xl md:text-2xl text-white/60 mb-8 md:mb-12 max-w-3xl mx-auto leading-relaxed"
              >
                Experience the power of dual-view task management with table and kanban boards
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8"
              >
                {user ? (
                  <Button
                    asChild
                    className="bg-orange-500/80 hover:bg-orange-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                  >
                    <div onClick={handleGetStarted} className="cursor-pointer">
                      <span className="relative z-10 flex items-center">
                        Go to Dashboard
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </span>
                      <span className="absolute inset-0 bg-gradient-to-r from-orange-600 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </Button>
                ) : (
                  <div className="flex justify-center">
                    <SignIn />
                  </div>
                )}
              </motion.div>

              {/* Trust indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-wrap justify-center gap-6 text-sm text-white/60"
              >
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                  <span>Instant access</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-rose-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                  <span>100% free forever</span>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay = 0,
  gradient
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
  gradient: string;
}) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      custom={delay}
      variants={fadeUpVariants}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      className="relative group"
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-lg`}></div>
      <div className="bg-white/[0.03] backdrop-blur-md p-6 md:p-8 rounded-2xl shadow-lg border border-white/[0.08] relative h-full flex flex-col group-hover:border-white/20 transition-colors duration-300">
        <div className="flex items-start gap-4 mb-5">
          <div className={`bg-gradient-to-br ${gradient} p-4 rounded-xl relative border border-white/10 shrink-0`}>
            <motion.div
              animate={{ rotate: [0, 5, 0, -5, 0] }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-white"
            >
              {icon}
            </motion.div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-white/90 mb-2">{title}</h3>
            <p className="text-white/60">{description}</p>
          </div>
        </div>

        {/* Bottom flourish */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-b-2xl`}></div>
      </div>
    </motion.div>
  );
};

const BenefitCard = ({
  title,
  description,
  delay = 0,
  gradient
}: {
  title: string;
  description: string;
  delay?: number;
  gradient: string;
}) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      custom={delay}
      variants={fadeUpVariants}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      className="relative group"
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-lg`}></div>
      <div className="bg-white/[0.03] backdrop-blur-md p-6 md:p-8 rounded-2xl shadow-lg border border-white/[0.08] relative h-full flex flex-col text-center group-hover:border-white/20 transition-colors duration-300">
        <h3 className="text-xl font-semibold text-white/90 mb-4">{title}</h3>
        <p className="text-white/60 flex-grow">{description}</p>

        {/* Bottom flourish */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-b-2xl`}></div>
      </div>
    </motion.div>
  );
};

// Background Components
const GlowingBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Large gradient orbs */}
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-br from-orange-500/20 via-red-500/10 to-transparent rounded-full blur-3xl opacity-70" />
      <div className="absolute top-1/4 -right-40 w-96 h-96 bg-gradient-to-bl from-red-500/20 via-rose-500/10 to-transparent rounded-full blur-3xl opacity-70" />
      <div className="absolute -bottom-40 left-1/4 w-80 h-80 bg-gradient-to-tr from-rose-500/20 via-orange-500/10 to-transparent rounded-full blur-3xl opacity-70" />

      {/* Radial gradients */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-full h-full bg-gradient-radial from-red-500/5 via-transparent to-transparent" />
      <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-radial from-orange-500/5 via-transparent to-transparent" />
    </div>
  );
};

const ElegantShape = ({ className, delay = 0 }: { className: string; delay?: number }) => {
  return (
    <motion.div
      className={`absolute rounded-full border border-white/10 ${className}`}
      animate={{
        rotate: [0, 360],
        scale: [1, 1.1, 1],
        opacity: [0.3, 0.6, 0.3],
      }}
      transition={{
        duration: 20,
        repeat: Infinity,
        ease: "linear",
        delay,
      }}
    />
  );
};

const FloatingElements = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <ElegantShape className="top-20 left-20 w-32 h-32" delay={0} />
      <ElegantShape className="top-40 right-32 w-24 h-24" delay={5} />
      <ElegantShape className="bottom-32 left-40 w-28 h-28" delay={10} />
      <ElegantShape className="bottom-20 right-20 w-20 h-20" delay={15} />

      {/* Floating dots */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-2 h-2 bg-orange-400/60 rounded-full"
        animate={{
          y: [0, -20, 0],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-1/3 right-1/3 w-3 h-3 bg-red-400/60 rounded-full"
        animate={{
          y: [0, 15, 0],
          opacity: [0.3, 0.7, 0.3],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-2 h-2 bg-rose-400/60 rounded-full"
        animate={{
          y: [0, -10, 0],
          opacity: [0.5, 0.9, 0.5],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />
    </div>
  );
};

export default TasksLanding;