import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Helmet } from "react-helmet-async";
import { useNavigate } from "react-router-dom";
import { BarChart3, TrendingUp, Calendar, Target, Clock, PieChart, LineChart, Activity } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import Header from "@/components/shared/Header";
import Footer from "@/components/shared/Footer";

// Animation variants
const fadeUpVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 1,
      delay: 0.5 + i * 0.2,
      ease: [0.25, 0.4, 0.25, 1],
    },
  }),
};

const AnalyticsLanding = () => {
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleGetStarted = async () => {
    if (user) {
      navigate('/analytics');
    } else {
      try {
        await signInWithGoogle();
        navigate('/analytics');
      } catch (error) {
        console.error('Sign in failed:', error);
      }
    }
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030303] font-onest flex flex-col min-h-screen">
      <Helmet>
        <title>Study Analytics & Progress Tracking | IsotopeAI</title>
        <meta
          name="description"
          content="Track your study progress with detailed analytics, visualize your learning patterns, and optimize your study habits with comprehensive insights and data-driven recommendations."
        />
        <meta
          name="keywords"
          content="study analytics, progress tracking, learning insights, study patterns, performance metrics, study statistics, academic analytics, learning dashboard, study visualization, progress charts, JEE analytics, NEET progress tracking"
        />
        <meta property="og:title" content="Study Analytics & Progress Tracking | IsotopeAI" />
        <meta property="og:description" content="Track your study progress with detailed analytics, visualize your learning patterns, and optimize your study habits with comprehensive insights." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/analytics-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Study Analytics & Progress Tracking | IsotopeAI" />
        <meta name="twitter:description" content="Track your study progress with detailed analytics and optimize your study habits." />
      </Helmet>

      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-[#030303]" />
        <div className="absolute inset-0 bg-gradient-to-br from-violet-950/20 via-transparent to-emerald-950/20" />
        <AnimatedBackground />
      </div>

      {/* Header */}
      <Header />

      {/* Hero Section */}
      <section className="relative pt-32 pb-20 md:pt-40 md:pb-32 overflow-hidden">
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
            >
              <BarChart3 className="h-4 w-4 text-violet-400" />
              <span className="font-medium">Data-driven study insights</span>
              <span className="ml-2 flex h-5 items-center justify-center rounded-full bg-green-500/20 px-2 text-xs font-medium text-green-400 ring-1 ring-inset ring-green-500/30">
                Real-time
              </span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight"
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                Transform Your{" "}
              </span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-emerald-400">
                Study Analytics
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-lg sm:text-xl text-white/60 mb-8 max-w-3xl mx-auto leading-relaxed"
            >
              Unlock the power of data to optimize your learning. Track progress, identify patterns, 
              and make informed decisions about your study habits with comprehensive analytics.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button
                onClick={handleGetStarted}
                className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-violet-500/25"
              >
                {user ? 'View Analytics' : 'Start Tracking'}
              </Button>
              <Button
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-full font-semibold transition-all duration-300"
                onClick={() => document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Explore Features
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Analytics Preview Section */}
      <AnalyticsPreviewSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* Stats Section */}
      <StatsSection />

      {/* CTA Section */}
      <CTASection handleGetStarted={handleGetStarted} user={user} />

      {/* Footer */}
      <Footer />
    </div>
  );
};

// Animated Background Component
const AnimatedBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <motion.div
        className="absolute top-1/4 left-1/4 w-64 h-64 bg-violet-500/10 rounded-full blur-3xl"
        animate={{
          x: [0, 100, 0],
          y: [0, -50, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-3/4 right-1/4 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl"
        animate={{
          x: [0, -80, 0],
          y: [0, 60, 0],
          scale: [1, 0.8, 1],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />
    </div>
  );
};

const AnalyticsPreviewSection = () => {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-violet-950/10 to-transparent"></div>
      
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            custom={0}
            variants={fadeUpVariants}
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
              Visualize Your{" "}
            </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-emerald-400">
              Learning Journey
            </span>
          </motion.h2>
          <motion.p
            custom={1}
            variants={fadeUpVariants}
            className="text-white/60 max-w-2xl mx-auto text-lg"
          >
            Get comprehensive insights into your study patterns with beautiful, interactive charts and detailed breakdowns
          </motion.p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.3 }}
          className="relative max-w-5xl mx-auto"
        >
          <div className="bg-white/[0.02] rounded-xl p-4 border border-white/5 backdrop-blur-sm">
            <img
              src="/analytics-preview.png"
              alt="Analytics Dashboard Preview"
              className="rounded-lg shadow-lg border border-white/10 w-full"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "https://placehold.co/800x500/0a0a2a/white?text=Analytics+Dashboard";
              }}
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

const FeaturesSection = () => {
  return (
    <section id="features" className="relative py-20 md:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-emerald-950/10 to-transparent"></div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            custom={0}
            variants={fadeUpVariants}
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
              Powerful{" "}
            </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-purple-400">
              Analytics Features
            </span>
          </motion.h2>
          <motion.p
            custom={1}
            variants={fadeUpVariants}
            className="text-white/60 max-w-2xl mx-auto text-lg"
          >
            Everything you need to understand and optimize your study performance
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          <FeatureCard
            icon={<BarChart3 className="w-6 h-6" />}
            title="Daily Progress"
            description="Track your daily study time, sessions, and achievements with detailed breakdowns"
            delay={0.1}
            gradient="from-violet-500/30 to-purple-600/30"
          />
          <FeatureCard
            icon={<TrendingUp className="w-6 h-6" />}
            title="Trend Analysis"
            description="Identify patterns and trends in your study habits over time"
            delay={0.2}
            gradient="from-emerald-500/30 to-teal-600/30"
          />
          <FeatureCard
            icon={<PieChart className="w-6 h-6" />}
            title="Subject Distribution"
            description="Visualize how you allocate time across different subjects"
            delay={0.3}
            gradient="from-rose-500/30 to-pink-600/30"
          />
          <FeatureCard
            icon={<Calendar className="w-6 h-6" />}
            title="Study Calendar"
            description="View your study activity in an interactive calendar format"
            delay={0.4}
            gradient="from-blue-500/30 to-indigo-600/30"
          />
          <FeatureCard
            icon={<Target className="w-6 h-6" />}
            title="Goal Tracking"
            description="Set and monitor your daily study targets and streaks"
            delay={0.5}
            gradient="from-orange-500/30 to-red-600/30"
          />
          <FeatureCard
            icon={<Activity className="w-6 h-6" />}
            title="Performance Metrics"
            description="Comprehensive metrics including focus time and productivity scores"
            delay={0.6}
            gradient="from-cyan-500/30 to-blue-600/30"
          />
        </div>
      </div>
    </section>
  );
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay,
  gradient
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
  gradient: string;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay }}
      className="group relative"
    >
      <div className={`p-8 rounded-2xl bg-gradient-to-br ${gradient} border border-white/10 transition-all duration-500 hover:bg-opacity-20 hover:border-white/20 h-full flex flex-col backdrop-blur-sm`}>
        <div className="w-16 h-16 rounded-2xl bg-white/10 flex items-center justify-center mb-6 border border-white/20 group-hover:bg-white/20 transition-all duration-300">
          <div className="text-white">{icon}</div>
        </div>

        <h3 className="text-2xl font-semibold text-white/90 mb-4">{title}</h3>
        <p className="text-white/60 mb-6 flex-grow leading-relaxed">{description}</p>

        {/* Bottom flourish */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-b-2xl`}></div>
      </div>
    </motion.div>
  );
};

const StatsSection = () => {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-violet-950/20 via-transparent to-emerald-950/20"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            custom={0}
            variants={fadeUpVariants}
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
              Proven{" "}
            </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-emerald-400">
              Results
            </span>
          </motion.h2>
          <motion.p
            custom={1}
            variants={fadeUpVariants}
            className="text-white/60 max-w-2xl mx-auto text-lg"
          >
            See how analytics-driven studying transforms academic performance
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
          <StatCard
            number="40%"
            label="Improvement in Study Efficiency"
            delay={0.1}
          />
          <StatCard
            number="3x"
            label="Better Goal Achievement Rate"
            delay={0.2}
          />
          <StatCard
            number="85%"
            label="Users Report Better Focus"
            delay={0.3}
          />
        </div>
      </div>
    </section>
  );
};

const StatCard = ({ number, label, delay }: { number: string; label: string; delay: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay }}
      className="text-center p-8 rounded-2xl bg-white/[0.02] border border-white/10 backdrop-blur-sm hover:bg-white/[0.04] transition-all duration-300"
    >
      <div className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-400 to-emerald-400 mb-4">
        {number}
      </div>
      <div className="text-white/70 text-lg font-medium">{label}</div>
    </motion.div>
  );
};

const CTASection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: any }) => {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-violet-950/20 via-transparent to-emerald-950/20"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <motion.h2
            custom={0}
            variants={fadeUpVariants}
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
              Ready to{" "}
            </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-emerald-400">
              Optimize Your Studies?
            </span>
          </motion.h2>

          <motion.p
            custom={1}
            variants={fadeUpVariants}
            className="text-lg sm:text-xl text-white/60 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            Start tracking your progress today and unlock insights that will transform your learning journey.
          </motion.p>

          <motion.div
            custom={2}
            variants={fadeUpVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              onClick={handleGetStarted}
              className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-violet-500/25"
            >
              {user ? 'Go to Analytics' : 'Get Started Free'}
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default AnalyticsLanding;
