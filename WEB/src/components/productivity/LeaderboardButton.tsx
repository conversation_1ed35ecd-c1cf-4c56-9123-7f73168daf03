import { useState } from 'react';
import { Trophy, Medal, Award, Crown, Star, Users, Flame, TrendingUp, Zap, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { supabase } from '@/integrations/supabase/client';

interface LeaderboardUser {
  id: string;
  displayName: string;
  username: string;
  photoURL: string;
  todayHours: number;
  totalHours: number;
  weeklyHours: number;
  streak: number;
}

export function LeaderboardButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [leaderboardType, setLeaderboardType] = useState<'daily' | 'weekly'>('daily');
  const [leaderboardUsers, setLeaderboardUsers] = useState<LeaderboardUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [processedCount, setProcessedCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const { user } = useSupabaseAuth();

  const fetchLeaderboardData = async () => {
    if (!user) return;

    setIsLoading(true);
    setLeaderboardUsers([]); // Clear existing data
    setProcessedCount(0);
    setTotalCount(0);

    try {
      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];

      // Calculate the start of current week (Sunday)
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - dayOfWeek);
      startOfWeek.setHours(0, 0, 0, 0);

      // Convert to YYYY-MM-DD format
      const weekStart = startOfWeek.toISOString().split('T')[0];

      console.log('Fetching leaderboard data for:', { today, weekStart });

      // Query all users from Supabase
      const { data: users, error } = await supabase
        .from('users')
        .select('id, username, display_name, photo_url, stats, progress');

      if (error) {
        console.error('Error fetching users:', error);
        setIsLoading(false);
        return;
      }

      const totalUsers = users?.length || 0;
      setTotalCount(totalUsers);
      console.log('Found users:', totalUsers);

      // Process users in parallel and update UI as each one completes
      const processUser = async (userData: any) => {
        try {
          // Get today's study hours from study_sessions table
          const { data: todaySessions, error: todayError } = await supabase
            .from('study_sessions')
            .select('duration, date')
            .eq('user_id', userData.id)
            .eq('date', today);

          if (todayError) {
            console.error(`Error fetching today sessions for user ${userData.id}:`, todayError);
          }

          // Get this week's study hours
          const { data: weekSessions, error: weekError } = await supabase
            .from('study_sessions')
            .select('duration, date')
            .eq('user_id', userData.id)
            .gte('date', weekStart);

          if (weekError) {
            console.error(`Error fetching week sessions for user ${userData.id}:`, weekError);
          }

          // Get all study sessions for total hours
          const { data: allSessions, error: allError } = await supabase
            .from('study_sessions')
            .select('duration')
            .eq('user_id', userData.id);

          if (allError) {
            console.error(`Error fetching all sessions for user ${userData.id}:`, allError);
          }

          // Calculate hours (duration is in seconds in Supabase)
          const todayHours = (todaySessions || []).reduce((total, session) => total + (session.duration || 0), 0) / 3600;
          const weeklyHours = (weekSessions || []).reduce((total, session) => total + (session.duration || 0), 0) / 3600;
          const totalHours = (allSessions || []).reduce((total, session) => total + (session.duration || 0), 0) / 3600;

          // Get streak from user data or calculate it
          const streak = userData.stats?.currentStreak || 0;

          // Only process users with valid usernames
          if (userData.username) {
            const newUser: LeaderboardUser = {
              id: userData.id,
              displayName: userData.display_name || userData.username || 'Anonymous',
              username: userData.username,
              photoURL: userData.photo_url || '',
              todayHours,
              totalHours,
              weeklyHours,
              streak
            };

            // Check if user meets criteria for current leaderboard type
            const meetsDaily = leaderboardType === 'daily' && todayHours >= 0.5;
            const meetsWeekly = leaderboardType === 'weekly' && weeklyHours >= 1;

            if (meetsDaily || meetsWeekly) {
              // Add user to leaderboard immediately and re-sort
              setLeaderboardUsers(prevUsers => {
                const updatedUsers = [...prevUsers, newUser];

                // Sort based on leaderboard type
                if (leaderboardType === 'daily') {
                  return updatedUsers.sort((a, b) => b.todayHours - a.todayHours);
                } else {
                  return updatedUsers.sort((a, b) => b.weeklyHours - a.weeklyHours);
                }
              });
            }
          }

          // Update processed count
          setProcessedCount(prev => prev + 1);
        } catch (userError) {
          console.error(`Error processing user ${userData.id}:`, userError);
          setProcessedCount(prev => prev + 1);
        }
      };

      // Process all users in parallel
      const userPromises = (users || []).map(processUser);
      await Promise.allSettled(userPromises);

      console.log('All users processed');
    } catch (error) {
      console.error('Error fetching leaderboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchLeaderboardData();
    }
  }, [isOpen, leaderboardType]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 0:
        return (
          <div className="relative">
            <Crown className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500 drop-shadow-lg" />
            <div className="absolute -top-0.5 -right-0.5 w-2 h-2 sm:w-3 sm:h-3 bg-yellow-400 rounded-full animate-pulse" />
          </div>
        );
      case 1:
        return <Medal className="h-5 w-5 sm:h-6 sm:w-6 text-gray-400 drop-shadow-md" />;
      case 2:
        return <Award className="h-5 w-5 sm:h-6 sm:w-6 text-amber-600 drop-shadow-md" />;
      default:
        return (
          <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center text-white text-xs font-bold">
            {rank + 1}
          </div>
        );
    }
  };

  const getRankBadge = (rank: number) => {
    if (rank === 0) return "🥇";
    if (rank === 1) return "🥈";
    if (rank === 2) return "🥉";
    return `#${rank + 1}`;
  };

  const getProgressColor = (rank: number) => {
    if (rank === 0) return "from-yellow-400 to-yellow-600";
    if (rank === 1) return "from-gray-300 to-gray-500";
    if (rank === 2) return "from-amber-400 to-amber-600";
    return "from-purple-400 to-purple-600";
  };

  // Find the current user in the leaderboard
  const currentUserRank = leaderboardUsers.findIndex(u => u.id === user?.id);

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="fixed z-50 bottom-28 right-4 sm:right-8 h-12 w-12 sm:h-14 sm:w-14 rounded-full shadow-xl border-2 border-yellow-400/30 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 backdrop-blur-md hover:from-yellow-100 hover:to-orange-100 dark:hover:from-yellow-800/30 dark:hover:to-orange-800/30 transition-all duration-300 hover:scale-110 leaderboard-trigger-button group"
        onClick={() => setIsOpen(true)}
      >
        <Trophy className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600 dark:text-yellow-400 group-hover:text-yellow-700 dark:group-hover:text-yellow-300 transition-colors duration-300" />
        <div className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 bg-red-500 rounded-full flex items-center justify-center">
          <Flame className="h-2 w-2 sm:h-2.5 sm:w-2.5 text-white" />
        </div>
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          hideDefaultClose
          className="w-[95vw] max-w-sm sm:max-w-md lg:max-w-lg max-h-[90vh] sm:max-h-[85vh] bg-gradient-to-br from-white via-purple-50/30 to-indigo-50/50 dark:from-slate-900 dark:via-slate-900/95 dark:to-slate-950 border-0 shadow-2xl shadow-purple-500/20 dark:shadow-purple-900/40 rounded-3xl sm:rounded-[2rem] overflow-hidden p-0"
        >
          <DialogHeader className="relative py-4 sm:py-6 px-4 sm:px-6 bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-700 text-white rounded-t-3xl sm:rounded-t-[2rem]">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600/90 via-indigo-600/90 to-purple-700/90 backdrop-blur-sm rounded-t-3xl sm:rounded-t-[2rem]" />
            <div className="relative flex items-center justify-between">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="relative">
                  <Trophy className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-300 drop-shadow-lg" />
                  <div className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-2 h-2 sm:w-3 sm:h-3 bg-yellow-400 rounded-full animate-pulse" />
                </div>
                <div>
                  <DialogTitle className="text-lg sm:text-2xl font-bold text-white drop-shadow-md">
                    Study Champions
                  </DialogTitle>
                  <p className="text-purple-100 text-xs sm:text-sm font-medium">
                    Rise to the top! 🚀
                  </p>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="group relative flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-all duration-300 hover:scale-110 border border-white/20 hover:border-white/30"
              >
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                {/* Custom Responsive Cross Icon */}
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-white/90 group-hover:text-yellow-300 transition-colors duration-200 relative z-10"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  aria-hidden="true"
                >
                  <circle cx="16" cy="16" r="15" stroke="currentColor" strokeWidth="2" opacity="0.15"/>
                  <line x1="10" y1="10" x2="22" y2="22" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round"/>
                  <line x1="22" y1="10" x2="10" y2="22" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round"/>
                </svg>
                <span className="sr-only">Close</span>
              </button>
            </div>
          </DialogHeader>

          <div className="px-3 sm:px-6 pb-3 sm:pb-6">
            <Tabs
              defaultValue="daily"
              value={leaderboardType}
              onValueChange={(value) => setLeaderboardType(value as 'daily' | 'weekly')}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 mb-3 sm:mb-6 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl sm:rounded-2xl p-0.5 sm:p-1 shadow-lg border border-white/20 dark:border-slate-700/50 h-10 sm:h-12">
                <TabsTrigger
                  value="daily"
                  className="rounded-lg sm:rounded-xl font-semibold text-xs sm:text-sm transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-purple-500/30 flex items-center gap-1 sm:gap-2 h-8 sm:h-10"
                >
                  <Target className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">Today</span>
                  <span className="xs:hidden">Day</span>
                </TabsTrigger>
                <TabsTrigger
                  value="weekly"
                  className="rounded-lg sm:rounded-xl font-semibold text-xs sm:text-sm transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-purple-500/30 flex items-center gap-1 sm:gap-2 h-8 sm:h-10"
                >
                  <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">This Week</span>
                  <span className="xs:hidden">Week</span>
                </TabsTrigger>
              </TabsList>

              <div className="bg-white/40 dark:bg-slate-800/40 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-white/30 dark:border-slate-700/30 shadow-xl overflow-hidden">
                <ScrollArea className="h-[45vh] sm:h-[40vh] lg:h-[35vh]">
                  <div className="p-3 sm:p-4 space-y-2 sm:space-y-3">
                    {isLoading && totalCount > 0 && (
                      <div className="text-center py-4 sm:py-6 px-3 sm:px-4">
                        <div className="flex items-center justify-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                          <div className="relative">
                            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-2 sm:border-4 border-purple-200 border-t-purple-600" />
                            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-indigo-400 opacity-20 animate-pulse" />
                          </div>
                          <div className="text-left">
                            <p className="font-semibold text-sm sm:text-base text-slate-700 dark:text-slate-300">Loading Champions...</p>
                            <p className="text-xs sm:text-sm text-slate-500 dark:text-slate-400">({processedCount}/{totalCount})</p>
                          </div>
                        </div>
                        <Progress
                          value={(processedCount / totalCount) * 100}
                          className="h-2 sm:h-3 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden"
                        />
                      </div>
                    )}

                    {leaderboardUsers.length > 0 ? (
                      leaderboardUsers.map((leaderboardUser, index) => {
                        const isCurrentUser = leaderboardUser.id === user?.id;
                        const hours = leaderboardType === 'daily' ? leaderboardUser.todayHours : leaderboardUser.weeklyHours;
                        const maxHours = leaderboardUsers[0] ? (leaderboardType === 'daily' ? leaderboardUsers[0].todayHours : leaderboardUsers[0].weeklyHours) : 1;
                        const progressPercentage = maxHours > 0 ? (hours / maxHours) * 100 : 0;

                        return (
                          <div
                            key={leaderboardUser.id}
                            className={cn(
                              "relative group transition-all duration-300 hover:scale-[1.02] hover:shadow-lg",
                              isCurrentUser && "ring-1 sm:ring-2 ring-purple-400 ring-offset-1 sm:ring-offset-2 ring-offset-white dark:ring-offset-slate-800"
                            )}
                          >
                            <div className={cn(
                              "relative overflow-hidden rounded-xl sm:rounded-2xl p-3 sm:p-4 backdrop-blur-sm border transition-all duration-300",
                              isCurrentUser
                                ? "bg-gradient-to-r from-purple-500/20 via-indigo-500/20 to-purple-500/20 border-purple-400/50 shadow-lg shadow-purple-500/20"
                                : index < 3
                                ? "bg-gradient-to-r from-white/80 to-white/60 dark:from-slate-800/80 dark:to-slate-800/60 border-white/50 dark:border-slate-700/50 hover:from-white/90 hover:to-white/80 dark:hover:from-slate-700/90 dark:hover:to-slate-700/80"
                                : "bg-white/60 dark:bg-slate-800/60 border-white/40 dark:border-slate-700/40 hover:bg-white/80 dark:hover:bg-slate-800/80"
                            )}>
                              {/* Rank Badge */}
                              <div className="absolute -top-1 -left-1 sm:-top-2 sm:-left-2 z-10">
                                <div className={cn(
                                  "w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold shadow-lg",
                                  index === 0 && "bg-gradient-to-br from-yellow-400 to-yellow-600 text-white",
                                  index === 1 && "bg-gradient-to-br from-gray-300 to-gray-500 text-white",
                                  index === 2 && "bg-gradient-to-br from-amber-400 to-amber-600 text-white",
                                  index > 2 && "bg-gradient-to-br from-purple-400 to-purple-600 text-white"
                                )}>
                                  {getRankBadge(index)}
                                </div>
                              </div>

                              <div className="flex items-center justify-between gap-2 sm:gap-0">
                                <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                                  <div className="flex items-center justify-center flex-shrink-0">
                                    {getRankIcon(index)}
                                  </div>

                                  <div className="relative flex-shrink-0">
                                    <div
                                      className="w-8 h-8 sm:w-12 sm:h-12 rounded-full bg-cover bg-center border-2 sm:border-3 border-white dark:border-slate-700 shadow-lg"
                                      style={{
                                        backgroundImage: leaderboardUser.photoURL
                                          ? `url(${leaderboardUser.photoURL})`
                                          : `url(https://ui-avatars.com/api/?name=${encodeURIComponent(leaderboardUser.username)}&background=random&size=48)`
                                      }}
                                    />
                                    {index < 3 && (
                                      <div className="absolute -bottom-0.5 -right-0.5 sm:-bottom-1 sm:-right-1 w-3 h-3 sm:w-4 sm:h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                                        <Star className="h-1.5 w-1.5 sm:h-2.5 sm:w-2.5 text-white" />
                                      </div>
                                    )}
                                  </div>

                                  <div className="flex-1 min-w-0">
                                    <p className="font-bold text-sm sm:text-base text-slate-800 dark:text-slate-200 truncate">
                                      @{leaderboardUser.username}
                                    </p>
                                    <div className="flex items-center gap-1 sm:gap-2 mt-0.5 sm:mt-1">
                                        {leaderboardUser.streak > 0 && (
                                        <div className="flex items-center gap-0.5 sm:gap-1">
                                          <Flame className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-red-500" />
                                          <span className="text-xs text-slate-600 dark:text-slate-400">
                                            {leaderboardUser.streak}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                <div className="text-right flex-shrink-0">
                                  <p className="font-bold text-base sm:text-lg lg:text-xl text-slate-800 dark:text-slate-200">
                                    {hours.toFixed(1)}
                                    <span className="text-xs sm:text-sm text-slate-500 dark:text-slate-400 ml-0.5 sm:ml-1">hrs</span>
                                  </p>
                                  <div className="mt-1 sm:mt-2 w-16 sm:w-20 lg:w-24">
                                    <div className="h-1.5 sm:h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                                      <div
                                        className={cn(
                                          "h-full rounded-full transition-all duration-500 bg-gradient-to-r",
                                          getProgressColor(index)
                                        )}
                                        style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {isCurrentUser && (
                                <div className="absolute inset-0 rounded-xl sm:rounded-2xl bg-gradient-to-r from-purple-500/10 via-transparent to-indigo-500/10 pointer-events-none" />
                              )}
                            </div>
                          </div>
                        );
                      })
                    ) : !isLoading ? (
                      <div className="text-center py-8 sm:py-12 px-3 sm:px-4">
                        <div className="relative mb-4 sm:mb-6">
                          <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/30 dark:to-indigo-900/30 rounded-full flex items-center justify-center">
                            <Users className="h-8 w-8 sm:h-10 sm:w-10 text-purple-400 dark:text-purple-300" />
                          </div>
                          <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce">
                            <Trophy className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                          </div>
                        </div>
                        <h3 className="text-base sm:text-lg font-bold text-slate-700 dark:text-slate-300 mb-2">
                          Be the First Champion!
                        </h3>
                        <p className="text-sm text-slate-500 dark:text-slate-400 mb-3 sm:mb-4">
                          No one has made it to the leaderboard yet.
                        </p>
                        <div className="inline-flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-full text-xs sm:text-sm font-semibold">
                          <Target className="h-3 w-3 sm:h-4 sm:w-4" />
                          Start studying to claim your spot!
                        </div>
                      </div>
                    ) : null}
                  </div>
                </ScrollArea>
              </div>

              {/* User Status Card */}
              <div className="mt-3 sm:mt-6">
                {currentUserRank !== -1 ? (
                  <div className="relative overflow-hidden rounded-2xl sm:rounded-3xl bg-gradient-to-r from-purple-500 via-indigo-500 to-purple-600 p-3 sm:p-4 text-white shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-600/50 via-indigo-600/50 to-purple-700/50 backdrop-blur-sm rounded-2xl sm:rounded-3xl" />
                    <div className="relative flex items-center justify-center gap-2 sm:gap-3">
                      <div className="w-8 h-8 sm:w-12 sm:h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <Trophy className="h-4 w-4 sm:h-6 sm:w-6 text-yellow-300" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm sm:text-lg font-bold">
                          You're #{currentUserRank + 1}!
                        </p>
                        <p className="text-purple-100 text-xs sm:text-sm">
                          Keep climbing! 🚀
                        </p>
                      </div>
                    </div>
                    <div className="absolute top-0 right-0 w-12 h-12 sm:w-20 sm:h-20 bg-yellow-400/20 rounded-full -translate-y-6 sm:-translate-y-10 translate-x-6 sm:translate-x-10" />
                    <div className="absolute bottom-0 left-0 w-8 h-8 sm:w-16 sm:h-16 bg-indigo-400/20 rounded-full translate-y-4 sm:translate-y-8 -translate-x-4 sm:-translate-x-8" />
                  </div>
                ) : (
                  <div className="relative overflow-hidden rounded-2xl sm:rounded-3xl bg-gradient-to-r from-slate-100 via-slate-50 to-slate-100 dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 p-3 sm:p-4 border border-slate-200 dark:border-slate-600">
                    <div className="flex items-center justify-center gap-2 sm:gap-3">
                      <div className="w-8 h-8 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-full flex items-center justify-center">
                        <Target className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm sm:text-lg font-bold text-slate-700 dark:text-slate-300">
                          Ready to Compete?
                        </p>
                        <p className="text-slate-500 dark:text-slate-400 text-xs sm:text-sm">
                          Start studying to join! 💪
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
